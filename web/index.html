<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->

  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Parenthing is an app that will help you raise happy, successful children.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Parenthing">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />

  <title>Parenthing Dashboard</title>
  <link rel="manifest" href="manifest.json">

  <style>
    /* Loader styles */
    #loader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .spinner {
      border: 8px solid #f3f3f3;
      border-top: 8px solid #5E57E1; /* Updated purple */
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  </style>

  <script>
    // The value below is injected by flutter build, do not touch.
    const serviceWorkerVersion = null;
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCSP2Se7lLMrsw2aNy4cdqHb3P0BPcZljs&libraries=places,drawing"></script>

  <script src="flutter.js" defer></script>
</head>

<body>

   <div id="loader">
    <div class="spinner"></div>
  </div>

  <script src="flutter_bootstrap.js" async>
    if ('serviceWorker' in navigator) {
      // Service workers are supported. Use them.
      window.addEventListener('load', function (ev) {
        // Register Firebase Messaging service worker.
        navigator.serviceWorker.register('firebase-messaging-sw.js', {
          scope: '/firebase-cloud-messaging-push-scope',
        });

      });
    }
  </script>
  
  <script>
    window.addEventListener('load', function (ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        entrypointUrl: "main.dart.js?v=" + serviceWorkerVersion,
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function (engineInitializer) {
          engineInitializer.initializeEngine().then(function (appRunner) {
            appRunner.runApp();
            // Hide the loader once Flutter app starts
            const loader = document.getElementById('loader');
            if (loader) {
              loader.style.display = 'none';
            }
          });
        }
      });
    });
  </script>
</body>

</html>