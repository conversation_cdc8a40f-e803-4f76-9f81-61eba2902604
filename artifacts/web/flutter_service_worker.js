'use strict';
const MANIFEST = 'flutter-app-manifest';
const TEMP = 'flutter-temp-cache';
const CACHE_NAME = 'flutter-app-cache';

const RESOURCES = {"icons/Icon-maskable-512.png": "301a7604d45b3e739efc881eb04896ea",
"icons/Icon-maskable-192.png": "c457ef57daa1d16f64b27b786ec2ea3c",
"icons/Icon-512.png": "5dc9750ae83e75a65f36a910e7e3128f",
"icons/Icon-192.png": "1dfe6eb879d395e4e600bc76e5cf896b",
"manifest.json": "aaa4a3a7a89ee7f5c7e5dc406a409c0b",
"canvaskit/canvaskit.js": "c86fbd9e7b17accae76e5ad116583dc4",
"canvaskit/canvaskit.js.symbols": "38cba9233b92472a36ff011dc21c2c9f",
"canvaskit/chromium/canvaskit.js": "43787ac5098c648979c27c13c6f804c3",
"canvaskit/chromium/canvaskit.js.symbols": "4525682ef039faeb11f24f37436dca06",
"canvaskit/chromium/canvaskit.wasm": "f5934e694f12929ed56a671617acd254",
"canvaskit/canvaskit.wasm": "3d2a2d663e8c5111ac61a46367f751ac",
"canvaskit/skwasm.wasm": "e42815763c5d05bba43f9d0337fa7d84",
"canvaskit/skwasm.js.symbols": "741d50ffba71f89345996b0aa8426af8",
"canvaskit/skwasm.js": "445e9e400085faead4493be2224d95aa",
"canvaskit/skwasm.worker.js": "bfb704a6c714a75da9ef320991e88b03",
"flutter.js": "c71a09214cb6f5f8996a531350400a9a",
"version.json": "b9422b6702d2db93ce9ed72c8ba25c1e",
"favicon.png": "a632706a6c4746348482603830ef6353",
"assets/packages/cupertino_icons/assets/CupertinoIcons.ttf": "e986ebe42ef785b27164c36a9abc7818",
"assets/NOTICES": "95c96c35236478fb2463c6f04e32762e",
"assets/AssetManifest.bin": "26de43cd32b93011b2dfaaed8f18a952",
"assets/FontManifest.json": "a3b44d9aea2e5cc6c61263f06d1d5ecc",
"assets/AssetManifest.bin.json": "3bf0f35487ba52ca270c0431d32e9a95",
"assets/assets/icons/WarningCircle.svg": "88dbbef7b6fbe848972945207f49de7a",
"assets/assets/icons/Link.svg": "24ac2c268f93ae4830954554241da613",
"assets/assets/icons/CalendarBlank.svg": "3beff3123b83550df4e1116216bdfb86",
"assets/assets/icons/arrow-left.svg": "1718f721e5853d10ca8ddced0c6379be",
"assets/assets/icons/admin_home_Active.svg": "6a3ceba90eec8fb6c1fa9201cf7f4745",
"assets/assets/icons/Checks.svg": "0701af85f83f3924d50b7f83a1b5f58d",
"assets/assets/icons/Trash.svg": "e41054b5bd7c8de922cb049e8dbc3a10",
"assets/assets/icons/Event.svg": "3fe34f8218e4d5186fc386ffc964c4b6",
"assets/assets/icons/admin_business_active.svg": "cf124b13d870b1c5e9d53e5338f37d5d",
"assets/assets/icons/admin_settings.svg": "0f65e26b110058647c466fa7ad236160",
"assets/assets/icons/VideoConference.svg": "571648e011c6ebaefb708b744653b42c",
"assets/assets/icons/empty_logo.svg": "e3db799c320342077ec10f27d2426ffe",
"assets/assets/icons/Flag.svg": "3896da745dc334ab31e416c9a77c8036",
"assets/assets/icons/PencilLine.svg": "03b011411b966635cb6cdbd0c6827e21",
"assets/assets/icons/admin_user.svg": "762f455817295f938b494605d38e0db6",
"assets/assets/icons/User.svg": "fe2318b1af593428d377da7c57c42133",
"assets/assets/icons/Phone.svg": "92d68484af9a0e1de83e66a75871bf21",
"assets/assets/icons/EyeSlash.svg": "84ada1a76fbed6079e9714d40aea229f",
"assets/assets/icons/MapPin.svg": "5ec55928a73920bba5859a02b42bfc0b",
"assets/assets/icons/admin_parents.svg": "477fd60eaf37f422fa354d960dd04e0b",
"assets/assets/icons/InstagramLogo.svg": "7f0db7ba41bd5339697852c964dd2fe4",
"assets/assets/icons/admin_parents_active.svg": "2555856ea6b8ec246e7c5230dc5f2153",
"assets/assets/icons/Trash_red.svg": "83e898c7fcc9c0eaa0f6f77d1172227b",
"assets/assets/icons/Vector.svg": "3309cc61f1806b389375db711de4ca7b",
"assets/assets/icons/admin_business.svg": "5e252d594fcbb561e9759c1624b12a89",
"assets/assets/icons/Classes.svg": "ac6b899be9e14d94848100f6741f04de",
"assets/assets/icons/admin_kyc.svg": "3ef94518d140e802e9ba97352ed59a46",
"assets/assets/icons/logout.svg": "8ec38d20ece2a55faa0f607661d6b345",
"assets/assets/icons/SignOut.svg": "724495fb2dc04f14efaa4ab25c955b81",
"assets/assets/icons/Envelope.svg": "54506a98d739ae29a27f2f00185a1a83",
"assets/assets/icons/ArrowsHorizontal.svg": "f114ec3df95dc2f35821752b784ce5af",
"assets/assets/icons/FacebookLogo.svg": "fac9e21de19ec8fcbabe06ec9aabc161",
"assets/assets/icons/Copy.svg": "9297acaad544083963f3ee7707d92de9",
"assets/assets/icons/Eye.svg": "dcff9707a87103970d4bd16644320d2d",
"assets/assets/icons/logo.svg": "b1fc70a2cb7bad47b4cb32302db7b771",
"assets/assets/icons/User_Active.svg": "88792413e64aa83f16a4f736cb6afd47",
"assets/assets/icons/YoutubeLogo.svg": "6fb6814d6f0d53f2ad7e2e826b2c5801",
"assets/assets/icons/admin_analytics.svg": "aa8ced28c19dd157311bbb540071cb69",
"assets/assets/icons/Event__Active.svg": "c69d9b241fc688945f3d499ca6f1d600",
"assets/assets/icons/kyc_icon.svg": "6f62a796c30b760b84069744b27f5087",
"assets/assets/icons/upload_picture.svg": "6366473a24cef43949b5dc00220d61e1",
"assets/assets/icons/PencilSimple.svg": "88642247f39e98c0dd3cc6bfd520a0c5",
"assets/assets/icons/Classes_Active.svg": "b111f2cf9efb91d22d04fe85648b99f0",
"assets/assets/png/app_logo.png": "d560d4f7f7b7f86092ef9f8c17844aad",
"assets/assets/png/logo.png": "f150e4f6973d022b42e4362d90ccfb01",
"assets/assets/png/slide_one.png": "61ebdb171b9aaf18155ce072e12eec1f",
"assets/assets/png/Banner_Placeholder.png": "329ce103664934668e0710af70e5d4ed",
"assets/assets/png/slide_two.png": "7e1d1b85bd6887f3b2f4c7bbece2f2b6",
"assets/assets/png/event_empty.png": "4c72bbb864a95bd4bc49b11d72c2d906",
"assets/assets/png/slide_three.png": "8e25c1c038b35d3b84f83747e15f810d",
"assets/assets/svg/CurrencyInr.svg": "83619999cc6df1755f8ba8207a17f5e3",
"assets/assets/svg/Info.svg": "cf2e5c8856f5b7973599683be2038dcd",
"assets/assets/svg/arrow-left.svg": "1718f721e5853d10ca8ddced0c6379be",
"assets/assets/svg/BG.svg": "cd659779a41cb3922d000aef705352c7",
"assets/assets/svg/new_business.svg": "bf0c30b6a3ad86c70c4cde8fbda1e17b",
"assets/assets/svg/Baby.svg": "c0bf6b7b58cbbb3ed31c3ead62675a69",
"assets/assets/svg/upload.svg": "f4ef9bfc0e8319a9c3938481afda7e0a",
"assets/assets/svg/User.svg": "e3743a357de95a017e2eb660274c07ec",
"assets/assets/svg/MapPin.svg": "1d6b569afea431f789406e546705bd96",
"assets/assets/svg/group.svg": "18cbc290a611cafc67ed35031ed349f1",
"assets/assets/svg/Classes.svg": "a8fdec29ccbf404fb4a8cd9ad9002e9a",
"assets/assets/svg/DotsThreeOutlineVertical.svg": "9cb70909a1737fd33d2970b7f9a67395",
"assets/assets/svg/kyc_rejected.svg": "2a0d975dcd2ccb42ff48a19e0790be0b",
"assets/assets/svg/ClockClockwise.svg": "6d6a38fa1b295b097fe82aac6368c57a",
"assets/assets/svg/logo_placeholder.svg": "d0b4d612f65f179dab77bf49fb13d725",
"assets/assets/svg/kyc_icon.svg": "6f62a796c30b760b84069744b27f5087",
"assets/assets/svg/Event__Inactive.svg": "433e4a73f727d3f0f514c3cc4ab6b003",
"assets/assets/svg/Calendar.svg": "d16d80a638a348fd6c345e75808a3a05",
"assets/assets/svg/app_logo.svg": "7d2083e131bc68ce6730c32ece8763e0",
"assets/assets/svg/PencilSimple.svg": "0a6ef77761e50aceeaff4c14dba14da8",
"assets/assets/svg/SealCheck.svg": "c3e1d8c1c7b45da84c690ac993ecb93e",
"assets/assets/svg/Buildings.svg": "d83e21f7eacd0955b004af8f58a68c5d",
"assets/assets/images/class_banner_empty.png": "c68319db116761bab6d0e4fd6386271e",
"assets/assets/images/PencilSimple.png": "1455a7ba860c4cc2cb1ca484e376e345",
"assets/assets/images/app_logo.png": "d28ef8a4eccf1342b094a9724c7b52e5",
"assets/assets/images/Plogo.png": "09fd51be4fdbb6e3519a554a1583416b",
"assets/assets/images/event_banner_empty.png": "3367a80fb60bc6b372d58e910280f9bf",
"assets/assets/images/BG.png": "0f5a7e032aecfbe0639a0a4c8ff5f650",
"assets/assets/images/threeDots.png": "18cd8ddc01f4e8444b642bd6ca296671",
"assets/assets/images/support_banner.png": "a0ba9dcf27ca0fa1605748ab88406bab",
"assets/assets/images/tips_banner.png": "61d47f1cfe3650fcc59570accbfa4eae",
"assets/assets/images/event_infographic.png": "b9379708def048e1d978a94c36e9c3e6",
"assets/fonts/Rubik-SemiBold.ttf": "742cf1e6b879de2de937aa287fddece4",
"assets/fonts/Rubik-Regular.ttf": "46df28800514364ef2766f74386b1bd3",
"assets/fonts/MaterialIcons-Regular.otf": "a96ca6fe8ef2ba0b907b61e73a75c343",
"assets/fonts/Rubik-Bold.ttf": "627d0e537f4a06a535ae956e4a87837f",
"assets/fonts/Rubik-Medium.ttf": "e785acbf5775e9bec2129f4967a75472",
"assets/AssetManifest.json": "761e8f56be78004a26ac699fb92c0690",
"assets/shaders/ink_sparkle.frag": "ecc85a2e95f5e9f53123dcaf8cb9b6ce",
"index.html": "399e4ecebde1548419ff87ce86354bf5",
"/": "399e4ecebde1548419ff87ce86354bf5",
"main.dart.js": "4da43c547a3922d68957aa037bd0a153"};
// The application shell files that are downloaded before a service worker can
// start.
const CORE = ["main.dart.js",
"index.html",
"assets/AssetManifest.bin.json",
"assets/FontManifest.json"];

// During install, the TEMP cache is populated with the application shell files.
self.addEventListener("install", (event) => {
  self.skipWaiting();
  return event.waitUntil(
    caches.open(TEMP).then((cache) => {
      return cache.addAll(
        CORE.map((value) => new Request(value, {'cache': 'reload'})));
    })
  );
});
// During activate, the cache is populated with the temp files downloaded in
// install. If this service worker is upgrading from one with a saved
// MANIFEST, then use this to retain unchanged resource files.
self.addEventListener("activate", function(event) {
  return event.waitUntil(async function() {
    try {
      var contentCache = await caches.open(CACHE_NAME);
      var tempCache = await caches.open(TEMP);
      var manifestCache = await caches.open(MANIFEST);
      var manifest = await manifestCache.match('manifest');
      // When there is no prior manifest, clear the entire cache.
      if (!manifest) {
        await caches.delete(CACHE_NAME);
        contentCache = await caches.open(CACHE_NAME);
        for (var request of await tempCache.keys()) {
          var response = await tempCache.match(request);
          await contentCache.put(request, response);
        }
        await caches.delete(TEMP);
        // Save the manifest to make future upgrades efficient.
        await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
        // Claim client to enable caching on first launch
        self.clients.claim();
        return;
      }
      var oldManifest = await manifest.json();
      var origin = self.location.origin;
      for (var request of await contentCache.keys()) {
        var key = request.url.substring(origin.length + 1);
        if (key == "") {
          key = "/";
        }
        // If a resource from the old manifest is not in the new cache, or if
        // the MD5 sum has changed, delete it. Otherwise the resource is left
        // in the cache and can be reused by the new service worker.
        if (!RESOURCES[key] || RESOURCES[key] != oldManifest[key]) {
          await contentCache.delete(request);
        }
      }
      // Populate the cache with the app shell TEMP files, potentially overwriting
      // cache files preserved above.
      for (var request of await tempCache.keys()) {
        var response = await tempCache.match(request);
        await contentCache.put(request, response);
      }
      await caches.delete(TEMP);
      // Save the manifest to make future upgrades efficient.
      await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
      // Claim client to enable caching on first launch
      self.clients.claim();
      return;
    } catch (err) {
      // On an unhandled exception the state of the cache cannot be guaranteed.
      console.error('Failed to upgrade service worker: ' + err);
      await caches.delete(CACHE_NAME);
      await caches.delete(TEMP);
      await caches.delete(MANIFEST);
    }
  }());
});
// The fetch handler redirects requests for RESOURCE files to the service
// worker cache.
self.addEventListener("fetch", (event) => {
  if (event.request.method !== 'GET') {
    return;
  }
  var origin = self.location.origin;
  var key = event.request.url.substring(origin.length + 1);
  // Redirect URLs to the index.html
  if (key.indexOf('?v=') != -1) {
    key = key.split('?v=')[0];
  }
  if (event.request.url == origin || event.request.url.startsWith(origin + '/#') || key == '') {
    key = '/';
  }
  // If the URL is not the RESOURCE list then return to signal that the
  // browser should take over.
  if (!RESOURCES[key]) {
    return;
  }
  // If the URL is the index.html, perform an online-first request.
  if (key == '/') {
    return onlineFirst(event);
  }
  event.respondWith(caches.open(CACHE_NAME)
    .then((cache) =>  {
      return cache.match(event.request).then((response) => {
        // Either respond with the cached resource, or perform a fetch and
        // lazily populate the cache only if the resource was successfully fetched.
        return response || fetch(event.request).then((response) => {
          if (response && Boolean(response.ok)) {
            cache.put(event.request, response.clone());
          }
          return response;
        });
      })
    })
  );
});
self.addEventListener('message', (event) => {
  // SkipWaiting can be used to immediately activate a waiting service worker.
  // This will also require a page refresh triggered by the main worker.
  if (event.data === 'skipWaiting') {
    self.skipWaiting();
    return;
  }
  if (event.data === 'downloadOffline') {
    downloadOffline();
    return;
  }
});
// Download offline will check the RESOURCES for all files not in the cache
// and populate them.
async function downloadOffline() {
  var resources = [];
  var contentCache = await caches.open(CACHE_NAME);
  var currentContent = {};
  for (var request of await contentCache.keys()) {
    var key = request.url.substring(origin.length + 1);
    if (key == "") {
      key = "/";
    }
    currentContent[key] = true;
  }
  for (var resourceKey of Object.keys(RESOURCES)) {
    if (!currentContent[resourceKey]) {
      resources.push(resourceKey);
    }
  }
  return contentCache.addAll(resources);
}
// Attempt to download the resource online before falling back to
// the offline cache.
function onlineFirst(event) {
  return event.respondWith(
    fetch(event.request).then((response) => {
      return caches.open(CACHE_NAME).then((cache) => {
        cache.put(event.request, response.clone());
        return response;
      });
    }).catch((error) => {
      return caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response != null) {
            return response;
          }
          throw error;
        });
      });
    })
  );
}
