const bool isLive = false;

const String baseUrl = isLive == true
    ? "https://prod-api.parenthingapp.co.in/dashboard"
    : "https://stag-api.parenthingapp.co.in/dashboard";

// const String baseUrl = isLive == true
//     ? "https://prod-api.parenthingapp.in/dashboard"
//     : "https://stag-api.parenthingapp.in/dashboard";

class AppUrl {
  static String googleMapsAPIKEY = "AIzaSyCSP2Se7lLMrsw2aNy4cdqHb3P0BPcZljs";
  // Auth
  static String sendOTPUrl = "$baseUrl/business/generateotp";
  static String verifyOTPUrl = "$baseUrl/business/verifyOtp";
  static String saveDeviceTokenUrl = "$baseUrl/business/saveDeviceTokenWeb";
  static String removeSavedDeviceTokenUrl = "$baseUrl/business/logOutWeb";
  static String businessUserById = "$baseUrl/business/getUserById";
  static String businessUserEditId = "$baseUrl/business/editProfile";

//homepage image
  static String homePageImageUrl = "$baseUrl/business/homepageimages";
  static String homePageGraphUrl =
      "$baseUrl/business/getBusinessClassEventsTotal";

  // event list api's
  static String createEventUrl = "$baseUrl/business/createEvent";
  static String eventListUrl = "$baseUrl/business/event/getListByBusinessId";
  static String eventDetailsUrl = "$baseUrl/business/event/getEventById";
  static String eventDeleteUrl = "$baseUrl/business/event/deleteEventById";
  static String eventEditUrl = "$baseUrl/business/editEventById";

  //notifications
  static String businessNotificationsUrl =
      "$baseUrl/business/getNotificationList";

  static String businessUpdateNotificationsUrl =
      "$baseUrl/business/updateNotification";

  // class category
  static const String classListUrl = '$baseUrl/business/classList';
  static const String createClassUrl = '$baseUrl/business/createClass';
  static const String classDetailsUrl = '$baseUrl/business/class/getClassById';
  static const String editClassUrl = '$baseUrl/business/editClassById';
  static const String deleteClassUrl =
      '$baseUrl/business/class/deleteClassById';
  static const String getCategory = '$baseUrl/business/getCategory';
  static const String kycSubmit = '$baseUrl/business/kyc';
  static const String classCategoryApi = '$baseUrl/business/getCategory';

  // kyc details
  static const String kycDetailsApi = '$baseUrl/business/getKycDetails';
  static const String businessLocationDeleteApi =
      '$baseUrl/business/deleteLocationbyId';

//upload image url

  static const String fileNameEntryUrl = isLive == true
      ? "https://prod-api.parenthingapp.co.in/upload/data"
      : "https://stag-api.parenthingapp.co.in/upload/data";

  // business profile
  static const String businessedit = "$baseUrl/business/editProfile";
  static const String businessbyID = "$baseUrl/business/getUserById";

  // location
  static const String addLocationUrl = "$baseUrl/business/addLocation";
  static const String editLocationUrl = "$baseUrl/business/editLocation";
  static const String getPlaceFromMap = "$baseUrl/business/get_places";

  // *************************** admin ********************//

  // Kyc
  static const String kycListApi = "$baseUrl/superadmin/kycList";
  static const String verifyKycApi = "$baseUrl/superadmin/verifyKyc";
  static const String kycRequestIDApi = "$baseUrl/superadmin/kycRequestById";

  // Business
  static const String businessListApi = "$baseUrl/superadmin/businessList";
  static const String eventListApi = "$baseUrl/superadmin/eventList";
  static const String classListApi = "$baseUrl/superadmin/classList";
  static const String reportListApi = "$baseUrl/superadmin/reportList";
  static const String adminBusinessbyID = "$baseUrl/superadmin/getBusinessById";
  static const String adminEventbyID = "$baseUrl/superadmin/eventRequestById";
  static const String adminApproveEventbyID =
      "$baseUrl/superadmin/approveRejectEvents";
  static const String adminApproveClassbyID =
      "$baseUrl/superadmin/approveRejectClass";
  static const String adminClassbyID = "$baseUrl/superadmin/classRequestById";
  static const String adminReportByEventID =
      "$baseUrl/superadmin/reportedEventById";
  static const String adminReportByClassID =
      "$baseUrl/superadmin/reportedClassById";
  static const String adminActionReportEventbyID =
      "$baseUrl/superadmin/approveRejectReported";

  //admin Home
  static const String pendingReviewApi = "$baseUrl/superadmin/home";
  static const String homePageGraph = "$baseUrl/superadmin/analytics";
  static const String analyticsPageGraph =
      "$baseUrl/superadmin/moduleAnalytics";

  //! Parents module
  static const String adminParentList = "$baseUrl/superadmin/parentsList";
  static const String adminInParentList = "$baseUrl/superadmin/inactiveParents";
  static const String adminReportedParentList =
      "$baseUrl/superadmin/reportedParentslist";
  static const String adminParentDetails = "$baseUrl/superadmin/getParentById";
  static const String adminUsersList = "$baseUrl/superadmin/adminList";
  static const String getAdminDetails = "$baseUrl/superadmin/getAdminById";
  static const String suspendParentUrl = "$baseUrl/superadmin/suspendParent";
  static const String appealParentUrl =
      "$baseUrl/superadmin/approveRejectReportedParent";
  static const String adminImageUploadUrl =
      "$baseUrl/superadmin/setHomepageImage";
  static const String adminEditProfilUrl = "$baseUrl/superadmin/editAdminById";
  static const String adminPositionUrl = "$baseUrl/superadmin/getRolesList";
  static const String createAdminUrl = "$baseUrl/superadmin/setAdminRoles";
  static const String addBusinessUrl = "$baseUrl/business/registerBusiness";

  static const String deleteAdminUser = "$baseUrl/superadmin/deleteAdminById";
}
