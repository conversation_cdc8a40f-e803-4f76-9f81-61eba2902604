import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/model/login/login_model.dart';
import 'package:parenthing_dashboard/model/user_model.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';
import 'package:parenthing_dashboard/view/common_widgets/loader.dart';

class BusinessController extends GetxController {
  RxBool isProfileDetailsLoading = true.obs;
  var loginModel = LoginModel().obs;
  RxBool showKycDialog = false.obs;
  RxBool showKycBanner = true.obs;
  var userModel = UserModel(location: []).obs;
  RxBool isLocationDeleted = true.obs;
  var userName = ''.obs;
  var userProfilePic = ''.obs;
  var userMobile = ''.obs;
  late ApiController apiController;

  @override
  void onInit() {
    super.onInit();
    apiController = Get.find<ApiController>();
    _checkKycBannerStatus();
    Future.delayed(Duration.zero, () {
      businessProfileDetails();
    });
  }

  void _checkKycBannerStatus() async {
    final hasDismissedKycBanner =
        storage.read('hasDismissedKycBanner') ?? false;
    if (hasDismissedKycBanner) {
      showKycBanner.value = false;
    }
  }

  void dismissKycBanner() {
    showKycBanner.value = false;
    storage.write('hasDismissedKycBanner', true);
  }

  Future<bool> businessProfileDetails(
      {bool isBusiness = false, String businessID = ''}) async {
    UserModel? userDataModel;
    try {
      Map<String, dynamic> payload = {
        "business_id": 0,
      };
      String? userId = storage.read('USER_ID');
      if (userId == null) {
        if (kDebugMode)
          log('❌ USER_ID is null. Cannot fetch business details.');
        return false;
      }
      var url = isBusiness
          ? "${AppUrl.businessbyID}/$businessID"
          : "${AppUrl.businessbyID}/$userId";
      var response =
          await apiController.apiHelperFn(apiUrl: url, payload: payload);

      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          userDataModel = UserModel.fromJson(jsonMap['data']['business']);
          userName.value =
              jsonMap['data']['business']['business_name'].toString();
          userProfilePic.value =
              jsonMap['data']['business']['profile_picture_url'].toString();
          userMobile.value = jsonMap['data']['business']['email'].toString();
          userModel.value = userDataModel;
          if (userModel.value.kycStatus == "") {
            showKycDialog.value = true;
          } else {
            showKycDialog.value = false;
          }
          isProfileDetailsLoading.value = false;

          return true;
        } else {
          userModel.value = UserModel(location: []);
          isProfileDetailsLoading.value = false;
          return false;
        }
      } else {
        userModel.value = UserModel(location: []);
        isProfileDetailsLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      userModel.value = UserModel(location: []);
      isProfileDetailsLoading.value = false;
      if (kDebugMode) {
        print("BusinessController ProfileDetails $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> businessEditProfileDetails() async {
    try {
      // Loader.showLoading();
      isProfileDetailsLoading.value = true;
      var response = await apiController.apiHelperFn(
          apiUrl: AppUrl.businessedit, payload: userModel.value);
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          userModel.value = UserModel.fromJson(jsonMap['data']);
          // Loader.hideLoading();
          isProfileDetailsLoading.value = false;
          return true;
        } else {
          userModel.value = UserModel(location: []);
          // Loader.hideLoading();
          isProfileDetailsLoading.value = false;
          return false;
        }
      } else {
        userModel.value = UserModel(location: []);
        // Loader.hideLoading();
        isProfileDetailsLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      userModel.value = UserModel(location: []);
      // Loader.hideLoading();
      isProfileDetailsLoading.value = false;
      if (kDebugMode) {
        print("BusinessController ProfileDetails $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> deleteBusinessAddress(int locationID) async {
    try {
      Loader.showLoading();
      isLocationDeleted.value = true;
      Map<String, dynamic> payload = {
        "business_id": int.parse(
          storage.read('USER_ID'),
        ),
        "location_id": locationID
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.businessLocationDeleteApi,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          Loader.hideLoading();
          isLocationDeleted.value = false;
        });
        return true;
      } else {
        Loader.hideLoading();
        isLocationDeleted.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      Loader.hideLoading();
      isLocationDeleted.value = false;
      if (kDebugMode) {
        print("=>fun createEvent $e \n  $stackTrace");
      }
      return false;
    }
  }
}
