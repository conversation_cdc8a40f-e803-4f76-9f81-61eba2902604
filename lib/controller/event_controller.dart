import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:parenthing_dashboard/model/event/create_event_model.dart';
import 'package:parenthing_dashboard/model/event/event_model.dart';
import 'package:parenthing_dashboard/model/location_model.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/view/common_widgets/loader.dart';
import 'package:http/http.dart' as http;

class EventController extends GetxController {
  String fileName = "EventController";
  final storage = GetStorage();
  String userID = "0";
  RxBool isEventCreateLoading = true.obs;
  RxBool isEventEditLoading = true.obs;
  RxBool isEventDeleteLoading = true.obs;
  RxBool isEventListLoading = true.obs;
  RxBool isEventDetailsLoading = true.obs;
  // static final http.Client _client = http.Client();
  var eventList = <EventModel>[].obs;
  var editEventModel = EventModel(locationDetails: LocationDetails()).obs;
  RxString uploadedFileName = "".obs;
  RxString responseFileUrl = "".obs;
  //event edit
  RxString startDate = "".obs;
  RxString startTime = "".obs;
  RxString endDate = "".obs;
  RxString endTime = "".obs;
  RxBool isStartDateLoading = false.obs;
  RxBool isEndDateLoading = false.obs;
  var eventDetailsModel = EventModel(locationDetails: LocationDetails()).obs;
  var createEventModel =
      CreateEventModel(locationDetails: LocationDetails()).obs;
  final startDateController = TextEditingController().obs;
  final startTimeController = TextEditingController().obs;
  final endDateController = TextEditingController().obs;
  final endTimeController = TextEditingController().obs;
  final durationController = TextEditingController().obs;
  final minAgeController = TextEditingController().obs;
  final maxAgeController = TextEditingController().obs;
  final eventTypeController = TextEditingController().obs;
  final cityController = TextEditingController().obs;
  final addressController = TextEditingController().obs;
  final ticketTypeController = TextEditingController().obs;
  final multiplepricingController = TextEditingController().obs;
  final maxAgController = TextEditingController().obs;
  final priceAtController = TextEditingController().obs;
  final ticketbookingController = TextEditingController().obs;
  final bookingURLController = TextEditingController().obs;
   late ApiController apiController;

@override
void onInit(){
  apiController = Get.find<ApiController>();
  super.onInit();
}


  List<String> serviceableCity = ['Mumbai', 'Pune', 'Hyderabad', 'Bangalore'];
  // List<String> serviceableAdd = [
  //   'Mumbai Branch',
  //   'Pune Branch',
  //   'Hyderabad Branch',
  //   'Bangalore Branch'
  // ];

  List<int> duration = [30, 45, 60, 75, 90, 105, 120];

  int? selectedDuration;

  String formatDate(String dateTime) {
    DateTime date = DateTime.parse(dateTime);
    return "${date.year}-${date.month}-${date.day}";
  }

  String formatTime(String dateTime) {
    DateTime date = DateTime.parse(dateTime);
    return "${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}";
  }

  Future<void> selectDateFn(BuildContext context, bool isStartDate) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: (isStartDate ? startDate.value : endDate.value) != ""
          ? DateTime.parse(isStartDate ? startDate.value : endDate.value)
          : DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null) {
      isStartDateLoading.value = true;
      if (isStartDate) {
        startDate.value = pickedDate.toString();
        log("DATE startDate  1::=> ${startDate.value}");
        isStartDateLoading.value = false;
        update();
      } else {
        endDate.value = pickedDate.toString();
        log("DATE endDate  1::=> ${endDate.value}");
        update();
      }
    }
    isStartDateLoading.value = false;
  }

  Future<void> selectTimeFn(BuildContext context, bool isStartTime) async {
    TimeOfDay initialSelectedTime = TimeOfDay.now();
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      barrierDismissible: false,
      initialTime: (isStartTime ? startTime.value : endTime.value) == ""
          ? initialSelectedTime
          : convertStringToTimeOfDay(
              isStartTime ? startTime.value : endTime.value),
      initialEntryMode: TimePickerEntryMode.dialOnly,
    );

    // Only show loading if a time was actually selected
    if (pickedTime != null) {
      isStartDateLoading.value = true;

      String formattedTime = _formatTime(pickedTime);
      if (isStartTime) {
        startTime.value = formattedTime;
      } else {
        endTime.value = formattedTime;
      }

      isStartDateLoading.value = false;
      update();
    }
  }

  String _formatTime(TimeOfDay timeOfDay) {
    // Format the TimeOfDay to "HH:mm" (24-hour format)
    String hour = timeOfDay.hour.toString().padLeft(2, '0');
    String minute = timeOfDay.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  TimeOfDay convertStringToTimeOfDay(String timeString) {
    List<String> parts = timeString.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1]);
    return TimeOfDay(hour: hours, minute: minutes);
  }

  //! create event fun
  Future<bool> createEvent(Map<String, dynamic> payload) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Loader.showLoading();
      isEventCreateLoading.value = true;
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.createEventUrl,
        payload: payload,
      );
      var jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        await Future.delayed(const Duration(milliseconds: 300));
        Loader.hideLoading();
        isEventCreateLoading.value = false;
        return true;
      } else {
        Loader.hideLoading();
        isEventCreateLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      Loader.hideLoading();
      isEventCreateLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>fun createEvent $e \n  $stackTrace");
      }
      return false;
    }
  }

  //! to get all event list fun
  Future<bool> getAllEventList(String type) async {
    isEventListLoading.value = true;
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "business_id": int.parse(userID),
        "type": type
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.eventListUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          List<EventModel> newList =
              data.map<EventModel>((e) => EventModel.fromJson(e)).toList();

          eventList.value = newList;
          isEventListLoading.value = false;
          return true;
        } else {
          eventList.value = [];
          isEventListLoading.value = false;
          return false;
        }
      } else {
        eventList.value = [];
        isEventListLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      eventList.value = [];
      isEventListLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>fun createEvent $e \n  $stackTrace");
      }
      return false;
    }
  }

  //! to get single event details by id
  Future<bool> getEventDetailsData(int eventID) async {
    EventModel eventModel;
    try {
      Map<String, dynamic> payload = {"event_id": eventID};
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.eventDetailsUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          eventModel = EventModel.fromJson(jsonMap['data']);
          eventDetailsModel.value = eventModel;
          Future.delayed(const Duration(milliseconds: 200)).then((value) {
            isEventDetailsLoading.value = false;
          });
          return true;
        } else {
          eventDetailsModel.value =
              EventModel(locationDetails: LocationDetails());
          Future.delayed(const Duration(milliseconds: 200)).then((value) {
            isEventDetailsLoading.value = false;
          });
          return false;
        }
      } else {
        eventDetailsModel.value =
            EventModel(locationDetails: LocationDetails());
        Future.delayed(const Duration(milliseconds: 200)).then((value) {
          isEventDetailsLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      eventDetailsModel.value = EventModel(locationDetails: LocationDetails());
      Future.delayed(const Duration(milliseconds: 200)).then((value) {
        isEventDetailsLoading.value = false;
      });
      if (kDebugMode) {
        print("$fileName=>fun createEvent $e \n  $stackTrace");
      }
      return false;
    }
  }

// to dispose the eventDetail model after use
  void clearEventData() {
    isEventDetailsLoading.value = true;
    eventDetailsModel.value = EventModel(locationDetails: LocationDetails());
  }

  //! to edit or modify event details fun
  Future<bool> editEventData(
      {required int publish,
      required String status,
      required int isApproved}) async {
    try {
      userID = storage.read("USER_ID") ?? "0";
      Loader.showLoading();
      isEventEditLoading.value = true;
      log("case 1");
      eventDetailsModel.value.businessId = userID;
      eventDetailsModel.value.publish = publish;
      eventDetailsModel.value.status = status;
      eventDetailsModel.value.isApproved = isApproved;
      var payload = eventDetailsModel.value;
      if (kDebugMode) {
        print("Edit Event Payload:::=> ${json.encode(payload)}");
      }
      var response = await apiController.apiHelperFn(
          apiUrl: AppUrl.eventEditUrl, payload: payload);
      log("case 2");
      // log(eventDetailsModel.toString());
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        log("case 3");
        log("Event Edited Successfully");
        await Future.delayed(const Duration(milliseconds: 300));
        Loader.hideLoading();
        isEventEditLoading.value = false;
        return true;
      } else {
        log("case 4");
        Loader.hideLoading();
        isEventEditLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      log("case 5");
      Loader.hideLoading();
      isEventEditLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>Edit Event $e \n  $stackTrace");
      }
      return false;
    }
  }

  //! to delete event details by id
  Future<bool> getEventDeleteData(int eventID) async {
    try {
      Loader.showLoading();
      isEventDeleteLoading.value = true;
      Map<String, dynamic> payload = {"event_id": eventID};
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.eventDeleteUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        Loader.hideLoading();
        isEventDeleteLoading.value = false;
        return true;
      } else {
        Loader.hideLoading();
        isEventDeleteLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      Loader.hideLoading();
      isEventDeleteLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>fun createEvent $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> createFileNameEntry(String fileName, String fileType,
      String filePath, Uint8List fileBytes, String imageUse,
      {bool isAdmin = false, String businessID = ''}) async {
    Loader.showLoading();
    log("Started createFileNameEntry with fileName: $fileName, fileType: $fileType, filePath: $filePath");
    try {
      String userID = storage.read("USER_ID") ?? "0";
      String id = isAdmin ? "admin_id" : "business_id";
      String idValue = businessID.isNotEmpty ? businessID : userID;

      String detectedContentType = _getContentTypeFromFileName(fileName);
      if (detectedContentType != fileType) {
        fileType = detectedContentType;
        log("Content type corrected to: $fileType");
      }

      Map<String, dynamic> payload = {
        id: idValue,
        "key": fileName,
        "ContentType": fileType,
        "image_usage": imageUse
      };
      log("Sending payload to ${AppUrl.fileNameEntryUrl}:\n${jsonEncode(payload)}");

      var response = await apiController.apiHelperFn(
          apiUrl: AppUrl.fileNameEntryUrl, payload: payload);

      if (response == null) {
        if (kDebugMode) {
          log("API response is null");
        }
        Loader.hideLoading();
        _showErrorSnackbar(
            "Network error. Please check your connection and try again.");
        return false;
      }

      log("Raw API Response: $response");

      var jsonMap = json.decode(response);

      if (jsonMap == null) {
        if (kDebugMode) {
          log("Failed to parse JSON response");
        }
        Loader.hideLoading();
        return false;
      }

      if (jsonMap['success'] == true) {
        if (jsonMap["data"] == null ||
            jsonMap["data"]["preSignedUrl"] == null) {
          if (kDebugMode) {
            print("PreSigned URL is null in API response");
          }
          Loader.hideLoading();
          return false;
        }
        String uploadImgPath = jsonMap["data"]["preSignedUrl"].toString();
        if (kDebugMode) {
          log("Upload Image Path: $uploadImgPath");
        }

        bool uploadResult =
            await uploadFileToS3(uploadImgPath, filePath, fileBytes, fileType);
        // if (uploadResult) {
        //   responseFileUrl.value =
        //       "https://profilemedia.s3.ap-south-1.amazonaws.com/${Uri.encodeComponent(fileName)}";
        //   if (kDebugMode) {
        //     print("File uploaded successfully to S3 with URL: ${responseFileUrl.value}");
        //   }
        // }
        Loader.hideLoading();
        return uploadResult;
      } else {
        Loader.hideLoading();
        String errorMsg = jsonMap['msg']?.toString() ?? "Unknown error";
        if (kDebugMode) {
          log("FileName entry creation failed: $errorMsg");
        }
        _showErrorSnackbar("Upload failed: $errorMsg");
        return false;
      }
    } catch (e) {
      Loader.hideLoading();
      if (kDebugMode) {
        log("Exception in createFileNameEntry: $e");
        log("Exception type: ${e.runtimeType}");
      }
      return false;
    }
  }

  String _getContentTypeFromFileName(String fileName) {
    String extension = fileName.toLowerCase().split('.').last;
    switch (extension) {
      case 'png':
        return 'image/png';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }

  void _showErrorSnackbar(String message) {
    Get.snackbar(
      "Upload Error",
      message,
      snackStyle: SnackStyle.FLOATING,
      backgroundColor: AppColors.errorRed,
      maxWidth: 300,
      colorText: AppColors.kwhite,
      duration: const Duration(seconds: 4),
    );
  }

  static Future<bool> uploadFileToS3(String uploadUrl, String filePath,
      Uint8List fileBytes, String contentType) async {
    log("Started uploadFileToS3 with uploadUrl: $uploadUrl");
    try {
      List<int> bytes;
      if (filePath.isNotEmpty) {
        bytes = await File(filePath).readAsBytes();
      } else {
        bytes = fileBytes;
      }

      log('Uploading ${bytes.length} bytes');
      log("PUT request to S3:\nURL: $uploadUrl\nContent-Length: ${bytes.length}");

      // Use the actual content type instead of hardcoded 'application/pdf'
      var response = await http.put(
        Uri.parse(uploadUrl),
        headers: {
          'Content-Type': contentType,
        },
        body: bytes,
      );

      log("S3 Upload Response Status Code: ${response.statusCode}");

      if (response.statusCode == 200) {
        log("File uploaded successfully to S3");
        return true;
      } else {
        log("S3 upload failed with status: ${response.statusCode}");
        log("S3 response body: ${response.body}");
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        log("Error in uploadFileToS3: $e");
      }
      return false;
    }
  }
}
