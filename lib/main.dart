import 'package:flutter/material.dart';
// ignore: depend_on_referenced_packages
import 'package:flutter_web_plugins/flutter_web_plugins.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:parenthing_dashboard/firebase/firebase_notifications.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/utils/binding.dart';
import 'package:parenthing_dashboard/view/welcome.dart';


late FirebaseService firebaseService;
late GetStorage storage;

void main() async {
  
  WidgetsFlutterBinding.ensureInitialized();
  usePathUrlStrategy();
  await GetStorage.init();
  storage = GetStorage();
  setupLocator();
  firebaseService = FirebaseService();
    firebaseService.initializeFirebaseAsync();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      // navigatorKey: locator<NavigationServices>().navigator<PERSON>ey,
      debugShowCheckedModeBanner: false,
      title: 'Parenthing Web',
      initialBinding: GlobalBindings(),
      theme: ThemeData(
        fontFamily: 'Rubic',
        useMaterial3: true,
        primarySwatch: Colors.deepPurple,
      ),
      home: const WelcomePage(),
    );
  }
}
