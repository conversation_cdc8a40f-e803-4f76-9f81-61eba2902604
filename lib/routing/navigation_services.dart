import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

class NavigationServices {
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  Future<dynamic> navigateTo(String routeName, {dynamic arguments}) async {
    try {
      final context = Get.context;
      if (context == null) {
        if (kDebugMode) {
          print('Navigation error: Context is null');
        }
        return null;
      }

      // Use go_router for navigation
      if (arguments != null) {
        context.push(routeName, extra: arguments);
      } else {
        context.push(routeName);
      }

      return null; // go_router doesn't return values like Navigator.pushNamed
    } catch (error) {
      if (kDebugMode) {
        print('Navigation error: $error');
      }
      return null;
    }
  }

  void goBack() {
    final context = Get.context;
    if (context != null && context.canPop()) {
      context.pop();
    }
  }

  void goBackWithResult(dynamic result) {
    final context = Get.context;
    if (context != null && context.canPop()) {
      context.pop(result);
    }
  }

  void navigateToReplacement(Widget page) {
    final context = Get.context;
    if (context != null) {
      // For replacement navigation, we'll use go instead of push
      // This maintains the same behavior as pushReplacement
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => page),
      );
    }
  }

  // Additional method for go_router specific navigation
  void goToRoute(String routeName, {dynamic extra}) {
    final context = Get.context;
    if (context != null) {
      if (extra != null) {
        context.go(routeName, extra: extra);
      } else {
        context.go(routeName);
      }
    }
  }

  // Method for replacing current route
  void replaceRoute(String routeName, {dynamic extra}) {
    final context = Get.context;
    if (context != null) {
      if (extra != null) {
        context.pushReplacement(routeName, extra: extra);
      } else {
        context.pushReplacement(routeName);
      }
    }
  }
}
