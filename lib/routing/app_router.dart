import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/admin_business_details_page.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/admin_business_page.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/admin_business_event_details.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/admin_business_class_details.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/admin_report_class_by_id.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/business_onboard/business_onboard_form.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/admin_create_event.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/admin_home_page.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/parents/admin_parents.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/parents/admin_parents_details_page.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/request/request_page.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/classes/classes_page.dart';
import 'package:parenthing_dashboard/view/classes/widget/class_details.dart';
import 'package:parenthing_dashboard/view/classes/widget/create_class_page.dart';
import 'package:parenthing_dashboard/view/classes/widget/edit_class_page.dart';
import 'package:parenthing_dashboard/view/common_widgets/notifications/notifications_page.dart';
import 'package:parenthing_dashboard/view/event/event_details.dart';
import 'package:parenthing_dashboard/view/event/event_page.dart';
import 'package:parenthing_dashboard/view/event/widgets/create_event.dart';
import 'package:parenthing_dashboard/view/event/widgets/event_edit.dart';
import 'package:parenthing_dashboard/view/help/help_page.dart';
import 'package:parenthing_dashboard/view/home_page/home_page.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kycpage.dart';
import 'package:parenthing_dashboard/view/landing_page/landing_page.dart';
import 'package:parenthing_dashboard/view/loginPage/business_onboard_page.dart';
import 'package:parenthing_dashboard/view/loginPage/loginpage.dart';
import 'package:parenthing_dashboard/view/profile/add_address.dart';
import 'package:parenthing_dashboard/view/profile/profile_page.dart';
import 'package:parenthing_dashboard/view/profile/widgets/edit_profile.dart';
import 'package:parenthing_dashboard/view/root/desktop_view_page.dart';
import 'package:parenthing_dashboard/view/welcome.dart';

// Global router instance
final GoRouter appRouter = GoRouter(
  initialLocation: '/',
  routes: [
    // Main routes
    GoRoute(
      path: '/',
      builder: (context, state) => const WelcomePage(),
    ),
    GoRoute(
      path: welcomeRoute,
      builder: (context, state) => const WelcomePage(),
    ),
    GoRoute(
      path: loginRoute,
      builder: (context, state) => const LoginPage(),
    ),
    GoRoute(
      path: homeRoute,
      builder: (context, state) => const HomePage(),
    ),
    GoRoute(
      path: businessRegister,
      builder: (context, state) => const BusinessOnBoardingPage(),
    ),
    GoRoute(
      path: loadingAnimationRoute,
      builder: (context, state) => const LoadingAnimationPage(),
    ),
    GoRoute(
      path: rootRoute,
      builder: (context, state) => const RootDashboardPage(),
    ),
    GoRoute(
      path: profileRoute,
      builder: (context, state) => const ProfilePage(),
    ),
    GoRoute(
      path: helpRoute,
      builder: (context, state) => const HelpPage(),
    ),
    GoRoute(
      path: kycFormPage,
      builder: (context, state) => const KycVerify(),
    ),
    GoRoute(
      path: businessEditPage,
      builder: (context, state) => const EditProfile(),
    ),
    
    // Event routes
    GoRoute(
      path: eventEditPage,
      builder: (context, state) {
        final args = state.extra as Map<String, dynamic>? ?? {};
        final bool isEdit = args.containsKey("eventID");
        final bool isDuplicateEvent = args.containsKey("isDuplicateEvent");
        return EventEditPage(
          isEdit: isEdit,
          eventID: isEdit ? args["eventID"] : null,
          isDuplicateEvent: isDuplicateEvent,
        );
      },
    ),
    GoRoute(
      path: createEventPage,
      builder: (context, state) => const CreateEventPage(),
    ),
    GoRoute(
      path: eventRoute,
      builder: (context, state) => const EventPage(),
    ),
    GoRoute(
      path: eventDetailsRoute,
      builder: (context, state) {
        final args = state.extra as Map<String, dynamic>;
        return EventDetailsPage(arguments: args);
      },
    ),
    
    // Class routes
    GoRoute(
      path: createclassPageRoute,
      builder: (context, state) => const CreateClassPage(),
    ),
    GoRoute(
      path: editclassRoute,
      builder: (context, state) {
        final args = state.extra as Map<String, dynamic>? ?? {};
        return EditClassPage(arguments: args);
      },
    ),
    GoRoute(
      path: classDetailsRoute,
      builder: (context, state) {
        final args = state.extra as Map<String, int>;
        return ClassDetails(arguments: args);
      },
    ),
    GoRoute(
      path: classesRoute,
      builder: (context, state) {
        final args = state.extra as Map<String, dynamic>?;
        if (args != null && args.containsKey('initialTab')) {
          return ClassesPage(initialTab: args['initialTab']);
        }
        return const ClassesPage();
      },
    ),
    
    // Other routes
    GoRoute(
      path: addAddressPage,
      builder: (context, state) {
        final args = state.extra;
        if (args is ProfileAddAddress) {
          return args;
        } else {
          return const ProfileAddAddress(isEditing: false);
        }
      },
    ),
    GoRoute(
      path: businessNotificationsPage,
      builder: (context, state) => const BusinessNotificationsPage(),
    ),
    GoRoute(
      path: createclassRoute,
      builder: (context, state) => const CreateClassPage(),
    ),
    GoRoute(
      path: notificationsRoute,
      builder: (context, state) => const BusinessNotificationsPage(),
    ),
  ],
);

// Admin router with permission checks
final GoRouter adminRouter = GoRouter(
  initialLocation: adminHomeRoute,
  routes: [
    GoRoute(
      path: adminHomeRoute,
      builder: (context, state) => const AdminHomePage(),
    ),
    GoRoute(
      path: adminBusinessDetailsRoute,
      builder: (context, state) {
        final args = state.extra as Map<String, int>;
        return AdminBusinessDetailsPage(arguments: args);
      },
    ),
    GoRoute(
      path: adminBusinessRoute,
      builder: (context, state) {
        final AdminUserController adminUserController = Get.find<AdminUserController>();
        final userPosition = adminUserController.adminDetailsModel.value.position;

        if (userPosition == "Moderator") {
          Get.snackbar(
            'Error',
            "You don't have access to this page.",
            snackPosition: SnackPosition.TOP,
            backgroundColor: AppColors.errorRed,
            maxWidth: 300,
            colorText: AppColors.kwhite,
          );
          return const AdminHomePage(); // Redirect to home
        }

        final args = state.extra as Map<String, dynamic>?;
        final int initialIndex = args?['initialIndex'] ?? 0;
        final String type = args?['type'] ?? 'events';
        return AdminBusinessPage(initialIndex: initialIndex, type: type);
      },
    ),
    GoRoute(
      path: adminEventDetailsPage,
      builder: (context, state) {
        final args = state.extra as Map<String, int>;
        return AdminEventDetailsPage(arguments: args);
      },
    ),
    GoRoute(
      path: adminClassDetailsPage,
      builder: (context, state) {
        final args = state.extra as Map<String, int>;
        return AdminClassDetailsPage(arguments: args);
      },
    ),
    GoRoute(
      path: adminReportClassDetailsPage,
      builder: (context, state) {
        final args = state.extra as Map<String, int>;
        return AdminReportClassDetailsPage(arguments: args);
      },
    ),
    GoRoute(
      path: adminCreateEventDetailsPage,
      builder: (context, state) => const AdminCreateEventPage(),
    ),
    GoRoute(
      path: businessOnBoardForm,
      builder: (context, state) => const BusinessOnBoardForm(),
    ),
    GoRoute(
      path: adminParentsRoute,
      builder: (context, state) {
        final args = state.extra as Map<String, dynamic>?;
        final int initialIndex = args?['initialIndex'] ?? 0;
        return AdminParentsPage(initialIndex: initialIndex);
      },
    ),
    GoRoute(
      path: adminParentsDetails,
      builder: (context, state) {
        final args = state.extra as Map<String, int>;
        final bool isParentProfile = state.extra is Map<String, dynamic>
            ? (state.extra as Map<String, dynamic>)['IS_PARENT_PROFILE'] ?? true
            : true;
        return AdminParentsDetails(arguments: args, isParentProfile: isParentProfile);
      },
    ),
    GoRoute(
      path: adminRequestRoute,
      builder: (context, state) => const RequestPage(),
    ),
  ],
);
