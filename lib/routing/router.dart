import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_analytics/admin_analytics_page.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/admin_business_class_details.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/admin_business_details_page.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/admin_business_page.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/admin_business_event_details.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/admin_report_class_by_id.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/admin_reported_event_details.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/admin_create_event.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/admin_home_page.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/business_onboard/business_onboard_form.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_profile/admin_profile.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_user/admin_user_page.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/parents/admin_parents.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/parents/admin_parents_details_page.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/request/request_page.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/classes/classes_page.dart';
import 'package:parenthing_dashboard/view/classes/widget/class_details.dart';
import 'package:parenthing_dashboard/view/classes/widget/create_class_page.dart';
import 'package:parenthing_dashboard/view/classes/widget/edit_class_page.dart';
import 'package:parenthing_dashboard/view/common_widgets/notifications/notifications_page.dart';
import 'package:parenthing_dashboard/view/event/event_details.dart';
import 'package:parenthing_dashboard/view/event/event_page.dart';
import 'package:parenthing_dashboard/view/event/widgets/create_event.dart';
import 'package:parenthing_dashboard/view/event/widgets/event_edit.dart';
import 'package:parenthing_dashboard/view/help/help_page.dart';
import 'package:parenthing_dashboard/view/home_page/home_page.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kycpage.dart';
import 'package:parenthing_dashboard/view/landing_page/landing_page.dart';
import 'package:parenthing_dashboard/view/loginPage/business_onboard_page.dart';
import 'package:parenthing_dashboard/view/loginPage/loginpage.dart';
import 'package:parenthing_dashboard/view/profile/add_address.dart';
import 'package:parenthing_dashboard/view/profile/profile_page.dart';
import 'package:parenthing_dashboard/view/profile/widgets/edit_profile.dart';
import 'package:parenthing_dashboard/view/root/desktop_view_page.dart';
import 'package:parenthing_dashboard/view/welcome.dart';

Route<dynamic>? genarateRoute(RouteSettings settings) {
  switch (settings.name) {
    case welcomeRoute:
      return _getPageRoute(const WelcomePage());
    case homeRoute:
      return _getPageRoute(const HomePage());
    case loginRoute:
      return _getPageRoute(const LoginPage());
    case businessRegister:
      return _getPageRoute(const BusinessOnBoardingPage());
    case loadingAnimationRoute:
      return _getPageRoute(const LoadingAnimationPage());
    case rootRoute:
      return _getPageRoute(const RootDashboardPage());
    case profileRoute:
      return _getPageRoute(const ProfilePage());
    case helpRoute:
      return _getPageRoute(const HelpPage());
    case kycFormPage:
      return _getPageRoute(const KycVerify());
    case businessEditPage:
      return _getPageRoute(const EditProfile());
    case eventEditPage:
      final args = settings.arguments as Map<String, dynamic>;
      final bool isEdit = args.containsKey("eventID");
      final bool isDuplicateEvent = args.containsKey("isDuplicateEvent");
      return _getPageRoute(EventEditPage(
          isEdit: isEdit,
          eventID: isEdit ? args["eventID"] : null,
          isDuplicateEvent: isDuplicateEvent));
    case createEventPage:
      return _getPageRoute(const CreateEventPage());
    case createclassPageRoute: //! need to remove after edit and create class in one page
      return _getPageRoute(const CreateClassPage());

    case editclassRoute:
      final args = settings.arguments as Map<String, dynamic>;
      return _getPageRoute(EditClassPage(
        arguments: args,
      ));
    case classDetailsRoute:
      final args = settings.arguments as Map<String, int>;
      return _getPageRoute(ClassDetails(arguments: args));
    case eventRoute:
      final args = settings.arguments;
      if (args != null &&
          args is Map<String, dynamic> &&
          args.containsKey('initialTab')) {
        return _getPageRoute(EventPage(initialTab: args['initialTab']));
      }
      return _getPageRoute(const EventPage());
    case eventDetailsRoute:
      final args = settings.arguments as Map<String, dynamic>;
      return _getPageRoute(EventDetailsPage(arguments: args));
    case addAddressPage:
      final args = settings.arguments;
      if (args is ProfileAddAddress) {
        return _getPageRoute(args);
      } else {
        return _getPageRoute(const ProfileAddAddress(isEditing: false));
      }
    case classesRoute:
      final args = settings.arguments;
      if (args != null &&
          args is Map<String, dynamic> &&
          args.containsKey('initialTab')) {
        return _getPageRoute(ClassesPage(initialTab: args['initialTab']));
      }
      return _getPageRoute(const ClassesPage());
    case businessNotificationsPage:
      return _getPageRoute(const BusinessNotificationsPage());
    default:
  }
  return null;
}

//! admin part
Route<dynamic>? adminGenarateRoute(RouteSettings settings) {
  final AdminUserController adminUserController =
      Get.find<AdminUserController>();

  final userPosition = adminUserController.adminDetailsModel.value.position;
  switch (settings.name) {
    case adminHomeRoute:
      return _getPageRoute(const AdminHomePage());
    case adminBusinessDetailsRoute:
      final args = settings.arguments as Map<String, int>;
      return _getPageRoute(AdminBusinessDetailsPage(arguments: args));
    case adminBusinessRoute:
      if (userPosition == "Moderator") {
        Get.snackbar(
          'Error',
          "You don't have access to this page.",
          snackPosition: SnackPosition.TOP,
          backgroundColor: AppColors.errorRed,
          maxWidth: 300,
          colorText: AppColors.kwhite,
        );
        return null;
      }
      final args = settings.arguments as Map<String, dynamic>?;
      final initialIndex = args?['initialIndex'] ?? 0;
      return _getPageRoute(AdminBusinessPage(initialIndex: initialIndex));
    case adminRequestRoute:
      return _getPageRoute(const RequestPage());
    case adminProfileRoute:
      return _getPageRoute(const AdminProfilePage());
    case adminParentsRoute:
      final args = settings.arguments as Map<String, dynamic>?;
      final initialIndex = args?['initialIndex'] ?? 0;
      return _getPageRoute(AdminParentsPage(initialIndex: initialIndex));
    case adminUsersRoute:
      return _getPageRoute(const AdminUserPage());
    case adminAnalyticsRoute:
      return _getPageRoute(const AdminAnalyticsPage());
    case adminEventDetailsPage:
      final args = settings.arguments as Map<String, int>;
      return _getPageRoute(AdminEventDetailsPage(arguments: args));
    case adminClassDetailsPage:
      final args = settings.arguments as Map<String, int>;
      return _getPageRoute(AdminClassDetailsPage(arguments: args));
    case adminParentsDetails:
      final args = settings.arguments as Map<String, dynamic>;
      final parentId = args['PARENT_ID'] as int;
      final isParentProfile = args['IS_PARENT_PROFILE'] as bool;
      return _getPageRoute(AdminParentsDetails(
          arguments: {'PARENT_ID': parentId},
          isParentProfile: isParentProfile));
    case adminReportEventDetails:
      final args = settings.arguments as Map<String, int>;
      return _getPageRoute(AdminReportEventDetailsPage(arguments: args));
    case adminReportClassDetailsPage:
      final args = settings.arguments as Map<String, int>;
      return _getPageRoute(AdminReportClassDetailsPage(arguments: args));
    case adminCreateEventDetailsPage:
      return _getPageRoute(const AdminCreateEventPage());
    case businessOnBoardForm:
      return _getPageRoute(const BusinessOnBoardForm());
    default:
  }
  return null;
}

PageRoute _getPageRoute(Widget child) {
  return _FadeRoute(child: child);
}

class _FadeRoute extends PageRouteBuilder {
  final Widget child;
  _FadeRoute({required this.child})
      : super(
          pageBuilder: (
            BuildContext context,
            Animation<double> animation,
            Animation<double> secondaryAnimation,
          ) =>
              child,
          transitionsBuilder: (
            BuildContext context,
            Animation<double> animation,
            Animation<double> secondaryAnimation,
            Widget child,
          ) =>
              FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
}
