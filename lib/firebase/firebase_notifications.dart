import 'dart:developer';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:parenthing_dashboard/firebase/firebase_options.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:gap/gap.dart';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();

  final GlobalKey<NavigatorState> navigatorKey;
  bool _notificationsEnabled = false;
  bool _isInitialized = false;
  bool _isInitializing = false;

  factory FirebaseService() => _instance;

  FirebaseService._internal() : navigatorKey = GlobalKey<NavigatorState>();

  Future<void> initializeFirebaseAsync() async {
    if (_isInitialized || _isInitializing) return;
    _isInitializing = true;
    try {
      log("Starting Firebase initialization...");
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      FirebaseMessaging messaging = FirebaseMessaging.instance;
      _setupFirebaseMessaging(messaging); // NOT awaited!
      _isInitialized = true;
      log("Firebase initialization complete.");
    } catch (e, stackTrace) {
      log("Error initializing Firebase: $e $stackTrace");
    } finally {
      _isInitializing = false;
    }
  }

  Future<void> ensureInitialized() async {
    if (_isInitialized) return;
    if (_isInitializing) {
      while (_isInitializing) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return;
    }
    await initializeFirebaseAsync();
  }

  Future<void> _setupFirebaseMessaging(FirebaseMessaging messaging) async {
    try {
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

      _requestNotificationPermissions(messaging).then((enabled) {
        _notificationsEnabled = enabled;
        if (enabled) {
          _getAndStoreToken(messaging);
          FirebaseMessaging.onMessageOpenedApp.listen(getMessages);
          FirebaseMessaging.onMessage.listen(getMessages);
          log("Firebase Messaging setup complete with notifications enabled.");
        } else {
          log("Firebase Messaging setup complete, but notifications are disabled.");
        }
      });
    } catch (e, stackTrace) {
      log("Error in Firebase Messaging setup: $e $stackTrace");
    }
  }

  Future<bool> _requestNotificationPermissions(FirebaseMessaging messaging) async {
    try {
      NotificationSettings settings = await messaging.requestPermission(
        alert: true, badge: true, sound: true,
      );
      return settings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      log("Error requesting notification permissions: $e");
      return false;
    }
  }

  Future<void> _getAndStoreToken(FirebaseMessaging messaging) async {
    if (!_notificationsEnabled) return;
    try {
      log("Attempting to get FCM token");
      String? deviceToken = await messaging.getToken();
      if (deviceToken != null) {
        await GetStorage().write('deviceToken', deviceToken);
        if (kDebugMode) print("FCM token:::=> $deviceToken");
      } else {
        if (kDebugMode) print("Failed to get FCM token");
      }
    } catch (e) {
      if (kDebugMode) print("Error getting FCM token: $e");
    }
  }

  static Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
    log("Handling a background message: ${message.messageId}");
    if (message.notification != null) {
      if (kDebugMode) print("Background notification Received");
    }
  }

  void getMessages(RemoteMessage message) {
    if (!_notificationsEnabled) return;
    if (kDebugMode) {
      print('NOTIFICATION ::=> onMessageOpenedAppFn:: ${message.data}');
    }
    _showFlutterNotification(message);
  }

  void _showFlutterNotification(RemoteMessage message) {
    if (!_notificationsEnabled) return;
    Map<String, dynamic> data = message.data;
    String? title = data['title'] ?? message.notification?.title;
    String? body = data['body'] ?? message.notification?.body;

    OverlayState? overlayState = navigatorKey.currentState?.overlay;
    if (overlayState == null) return;

    OverlayEntry overlayEntry = OverlayEntry(
      builder: (context) => CustomNotification(
        title: title ?? 'New Notification',
        body: body ?? '',
      ),
    );

    overlayState.insert(overlayEntry);
    Future.delayed(const Duration(seconds: 4), () {
      if (overlayEntry.mounted) overlayEntry.remove();
    });
  }

  static Future<String?> getDeviceToken() async {
    final instance = FirebaseService();
    await instance.ensureInitialized();
    if (!instance._notificationsEnabled) return null;
    return await FirebaseMessaging.instance.getToken();
  }

  bool get isInitialized => _isInitialized;
  bool get notificationsEnabled => _notificationsEnabled;
}

class CustomNotification extends StatefulWidget {
  final String title;
  final String body;
  const CustomNotification({super.key, required this.title, required this.body});

  @override
  State<CustomNotification> createState() => _CustomNotificationState();
}

class _CustomNotificationState extends State<CustomNotification> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);
    _slideAnimation = Tween<Offset>(begin: const Offset(1.0, 0.0), end: Offset.zero).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOutBack),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 20,
      right: 20,
      child: SlideTransition(
        position: _slideAnimation,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Material(
            color: Colors.transparent,
            child: Container(
              width: Get.width * 0.3,
              constraints: const BoxConstraints(minWidth: 300, maxWidth: 400),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.scaffoldColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 10, offset: const Offset(0, 5),
                  ),
                ],
                border: Border.all(color: Colors.grey.withOpacity(0.2), width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Icon(Icons.notifications_active, color: Theme.of(context).primaryColor, size: 20),
                      const Gap(8),
                      Expanded(
                        child: Text(widget.title,
                          overflow: TextOverflow.ellipsis, maxLines: 1,
                          style: title3TextSemiBold.copyWith(fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                  const Gap(8),
                  Text(widget.body,
                    overflow: TextOverflow.ellipsis, maxLines: 2,
                    style: bodyTextRegular.copyWith(fontSize: 14),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}