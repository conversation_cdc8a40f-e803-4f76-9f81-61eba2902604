import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/model/class/class_model.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';

class ClassCell extends StatelessWidget {
  const ClassCell({
    super.key,
    required this.classModelObject,
    required this.currentTab,
    this.isAdmin = false,
    required this.classVM
  });
  final ClassModel classModelObject;
  final String currentTab;
  final bool isAdmin;
final ClassController classVM;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
        child: GestureDetector(
      onTap: () {
        isAdmin
            ? locator<NavigationServices>().navigateTo(adminClassDetailsPage,
                arguments: <String, int>{
                    'requestID': classModelObject.requestID!
                  })
            : locator<NavigationServices>().navigateTo(classDetailsRoute,
                arguments: <String, int>{
                    'classByID': classModelObject.classId
                  }).then((value) {
                if (currentTab == "published") {
                  classVM.getAllClassList("published");
                } else if (currentTab == "draft") {
                  classVM.getAllClassList("draft");
                }
              });
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.scaffoldColor,
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(10.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              //mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Stack(
                    children: [
                      CachedNetworkImage(
                          imageUrl: classModelObject.bannerUrl,
                          width: 90,
                          height: 90,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Image.asset(
                              "assets/png/class_list_empty.png",
                              height: 90,
                              width: 90,
                              fit: BoxFit.cover),
                          errorWidget: (context, url, error) => Image.asset(
                              "assets/png/class_list_empty.png",
                              height: 90,
                              width: 90,
                              fit: BoxFit.cover)),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          height: 26,
                          decoration: BoxDecoration(
                            color: classModelObject.status == 'draft'
                                ? AppColors.kwarning
                                : classModelObject.isApproved == 1 &&
                                        classModelObject.status == 'approved'
                                    ? AppColors.lightGreen
                                    : classModelObject.isApproved == 0 &&
                                            classModelObject.status ==
                                                'inreview'
                                        ? AppColors.information
                                        : AppColors.lightPink,
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Center(
                            child: Text(
                              classModelObject.status
                                  .replaceAll("inreview", "In-review")
                                  .capitalizeFirst
                                  .toString(),
                              style: body3TextMedium,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                smMinWidth,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        classModelObject.title,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: bodyTextMedium,
                      ),
                      const Gap(5),
                      Text(
                        classModelObject.category.capitalizeFirst.toString(),
                        style: body2TextRegular,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                      const Gap(5),
                      Text(
                        classModelObject.classType.capitalizeFirst.toString(),
                        style: body2TextRegular,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      )
                    ],
                  ),
                ),
              ],
            ),
            const Gap(12),
            Container(
              height: .2,
              width: Get.width,
              color: AppColors.kblack.withOpacity(0.5),
            ),
            const Gap(12),
            Row(
              children: [
                classModelObject.classType == 'online'
                    ? const ClassRowChild(
                        iconPath: "assets/icons/VideoConference.svg",
                        textTitle: "Online")
                    : Expanded(
                        child: ClassRowChild(
                          iconPath: "assets/svg/MapPin.svg",
                          textTitle: classModelObject.city,
                        ),
                      ),
                Get.width <= 820 ? const SizedBox.shrink() : const Gap(6),
                Get.width >= 1024
                    ? Container(
                        height: Get.width * .03,
                        width: .2,
                        color: AppColors.kblack.withOpacity(0.5))
                    : const SizedBox.shrink(),
                Get.width <= 820 ? const SizedBox.shrink() : const Gap(6),
                Expanded(
                  child: ClassRowChild(
                    iconPath: "assets/svg/Baby.svg",
                    textTitle:
                        "${classModelObject.minAge.toString()}-${classModelObject.maxAge.toString().replaceAll("19", "18+")} years",
                  ),
                ),
                Get.width <= 820 ? const SizedBox.shrink() : const Gap(6),
                Get.width >= 1024
                    ? Container(
                        height: Get.width * .03,
                        width: .2,
                        color: AppColors.kblack.withOpacity(0.5))
                    : const SizedBox.shrink(),
                Get.width <= 820 ? const SizedBox.shrink() : const Gap(6),
                Expanded(
                  child: ClassRowChild(
                    iconPath: "assets/svg/group.svg",
                    textTitle:
                        '${classModelObject.sessionType.capitalizeFirst} class',
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    ));
  }
}

class ClassRowChild extends StatelessWidget {
  const ClassRowChild(
      {super.key, required this.iconPath, required this.textTitle});
  final String iconPath;
  final String textTitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(
          iconPath,
          height: 20.0,
          width: 20,
        ),
        const Gap(6),
        Text(
          textTitle,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          style: body2TextRegular.copyWith(color: AppColors.txtsecondary),
        ),
      ],
    );
  }
}
