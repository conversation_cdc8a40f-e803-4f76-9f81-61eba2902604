import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/model/class/class_model.dart';
import 'package:parenthing_dashboard/model/location_model.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/common_widgets/confirm_popup.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:parenthing_dashboard/view/profile/add_address.dart';

class CreateClassPage extends StatefulWidget {
  const CreateClassPage({super.key});

  @override
  State<CreateClassPage> createState() => _CreateClassPageState();
}

class _CreateClassPageState extends State<CreateClassPage> {
  final BusinessController businessController = Get.find<BusinessController>();
  RxBool isSelected = true.obs;
  RxBool isOfflineSelected =
      false.obs; //! make this true for by default class offline
  RxBool isOnlineSelected = true.obs;
  RxBool isOfflineOnlineSelected = false.obs;
  RxBool isPaidSelected = false.obs;
  RxBool isFreeSelected = false.obs;
  RxBool isTicketPaid = true.obs;
  RxBool isTicketFree = false.obs;
  RxBool isMultipleTicketYes = false.obs;
  RxBool isMultipleTicketNo = false.obs;
  RxBool isGroupSession = true.obs;
  RxBool is1Session = false.obs;
  RxBool isGroup1Session = false.obs;
  String selectedAddress = '';
  String selectedCity = "";

  final List<String> _ageOptions =
      List<String>.generate(17, (index) => (index).toString());
  final List<String> _maxAgeOptions =
      List<String>.generate(17, (index) => (index + 2).toString())..add('18+');
  String? _selectedMinAge;
  String? _selectedMaxAge;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final ClassController classVM = Get.find<ClassController>();
  final EventController eventController = Get.find<EventController>();
  // Map<String, dynamic> locationMap = jsonDecode(location);
  ClassModel? _createClassModel;

  // Uint8List? _imageBytes;
  String? _imageUrl;
  // final ImagePicker _picker = ImagePicker();

  final TextEditingController _descriptionController = TextEditingController();
  final int _maxLength = 500;
  final TextEditingController _titleController = TextEditingController();
  final int _maxTitleLength = 50;

  @override
  void initState() {
    super.initState();
    classVM.getClassCategoryListData();
    businessController.businessProfileDetails();
    _createClassModel = ClassModel(locationDetails: LocationDetails());
    _descriptionController.addListener(_updateDescCharaCount);
    _titleController.addListener(_updatetitleCharaCount);
    // _createClassModel!.classType = "offline";  //! to make the class offline by default
    _createClassModel!.classType =
        "online"; //! to make the class online by default
    _createClassModel!.sessionType = "Group";
    // _initializeDefaultAddress();
  }

  void _initializeDefaultAddress() {
    if (businessController.userModel.value.location.isNotEmpty) {
      var firstLocation = businessController.userModel.value.location.first;
      selectedAddress = firstLocation.address;
      var locationDetails = LocationDetails(
        area: firstLocation.area,
        city: firstLocation.city,
        state: firstLocation.state,
        address: firstLocation.address,
        country: firstLocation.country,
        latitude: double.parse(firstLocation.latitude),
        pinCode: firstLocation.pinCode,
        longitude: double.parse(firstLocation.longitude),
        locationId: firstLocation.locationId,
        subLocality: firstLocation.subLocality,
      );
      _createClassModel!.city = firstLocation.city;
      _createClassModel!.locationDetails = locationDetails;
      _createClassModel!.address = firstLocation.address;
    }
  }

  void _updateDescCharaCount() {
    setState(() {});
  }

  void _updatetitleCharaCount() {
    setState(() {});
  }

  @override
  void dispose() {
    _descriptionController.removeListener(_updateDescCharaCount);
    _titleController.removeListener(_updatetitleCharaCount);
    _descriptionController.dispose();
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              width: screenWidth <= 820 ? Get.width : Get.width * .7,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  width: 1.0,
                  color: AppColors.ktertiary.withOpacity(.4),
                ),
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        children: [
                          InkWell(
                            onTap: () {
                              showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (BuildContext context) {
                                  return CustomDialog(
                                    onConfirmTxt: "Yes, leave",
                                    onCancelText: "No",
                                    title: 'Leave this page?',
                                    content:
                                        'Are you sure you want to leave this page? All field details will be discarded',
                                    image: "assets/icons/WarningCircle.svg",
                                    onConfirm: () {
                                      Navigator.of(context).pop();
                                      locator<NavigationServices>().goBack();
                                    },
                                    onCancel: () {
                                      Navigator.of(context).pop();
                                    },
                                  );
                                },
                              );
                              //    showDialog(
                              //   context: Get.context!,
                              //   builder: (BuildContext context) {
                              //     return CommonConfermationDialog(
                              //       onTap: () {
                              //         Navigator.pop(context);

                              //         locator<NavigationServices>().goBack();
                              //       },
                              //       title: 'Are you sure you want to go back ?',
                              //     );
                              //   },
                              // );
                            },
                            child: SvgPicture.asset(
                                'assets/icons/arrow-left.svg',
                                fit: BoxFit.fill,
                                height: 32,
                                width: 32),
                          ),
                          const SizedBox(width: 16),
                          Text("Create a Class", style: heading2TextRegular),
                          // const Spacer(),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.end,
                          //   children: [
                          //     SecondaryButton(
                          //       textColor: businessController
                          //                   .userModel.value.kycDone ==
                          //               0
                          //           ? AppColors.ktertiary
                          //           : AppColors.kprimarycolor,
                          //       backgroundColor: businessController
                          //                   .userModel.value.kycDone ==
                          //               0
                          //           ? AppColors.scaffoldColor
                          //           : AppColors.kwhite,
                          //       text: 'Save as draft',
                          //       onTap: () async {
                          //         if (businessController
                          //                 .userModel.value.kycDone ==
                          //             1) {
                          //           onSubmitOrSaveDraft(false);
                          //         }
                          //       },
                          //     ),
                          //     const Gap(20),
                          //     PrimaryButton(
                          //       textColor: businessController
                          //                   .userModel.value.kycDone ==
                          //               0
                          //           ? AppColors.ktertiary
                          //           : AppColors.kwhite,
                          //       backgroundColor: businessController
                          //                   .userModel.value.kycDone ==
                          //               0
                          //           ? AppColors.scaffoldColor
                          //           : AppColors.kprimarycolor,
                          //       text: 'Submit for review',
                          //       onTap: () async {
                          //         if (businessController
                          //                 .userModel.value.kycDone ==
                          //             1) {
                          //           onSubmitOrSaveDraft(true);
                          //         }
                          //       },
                          //     ),
                          //   ],
                          // ),
                        ],
                      ),
                    ),
                    const Divider(
                      thickness: 1.0,
                      color: AppColors.kgrey,
                    ),
                    screenWidth <= 820
                        ? Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildClassBannerField(),
                                const Gap(20),
                                _buildClassNameField(),
                              ],
                            ),
                          )
                        : Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              children: [
                                Expanded(child: _buildClassNameField()),
                                const Gap(20),
                                _buildClassBannerField(),
                              ],
                            ),
                          ),
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Obx(
                              () => CustomDropdownUI(
                                isValidatorReq: true,
                                title: "Category",
                                items: classVM.classCategoryList
                                    .where((e) => e.name != "All")
                                    .map((e) {
                                  return DropdownMenuItem(
                                    onTap: () {
                                      classVM.updateSubCategories(e.name);
                                    },
                                    value: e.name,
                                    child: Text(
                                      e.name.toString(),
                                      style: body2TextBold,
                                    ),
                                  );
                                }).toList(),
                                value: classVM
                                            .selectedClassCategoryName.value ==
                                        ''
                                    ? null
                                    : classVM.selectedClassCategoryName.value,
                                onChanged: (v) {
                                  setState(() {
                                    classVM.selectedClassCategoryName.value ==
                                        v;
                                    log(v.toString());
                                    _createClassModel!.category = v as String;
                                    log('Updated Model Category: ${_createClassModel!.category}');
                                  });
                                },
                              ),
                            ),
                          ),
                          const Gap(20),
                          Expanded(
                            child: CustomDropdownUI(
                              isValidatorReq: true,
                              title: "Sub category",
                              items: classVM.classSubCategories.map((e) {
                                return DropdownMenuItem(
                                  onTap: () {},
                                  value: e.name,
                                  child: Text(
                                    e.name.toString(),
                                    style: body2TextBold,
                                  ),
                                );
                              }).toList(),
                              value: classVM
                                          .selectedSubClassCategoryName.value ==
                                      ''
                                  ? null
                                  : classVM.selectedSubClassCategoryName.value,
                              onChanged: (v) {
                                setState(() {
                                  classVM.selectedSubClassCategoryName.value ==
                                      v;
                                  log(v.toString());
                                  _createClassModel!.subCategory = v as String;
                                  log('Updated Model SubCategory: ${_createClassModel!.subCategory}');
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Gap(10),
                    const Divider(thickness: 1.0, color: AppColors.kgrey),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        screenWidth <= 820
                            ? Column(
                                children: [
                                  buildclassAgeGroup(),
                                  _buildclassType()
                                ],
                              )
                            : Row(
                                children: [
                                  Expanded(child: buildclassAgeGroup()),
                                  Expanded(child: _buildclassType())
                                ],
                              ),
                      ],
                    ),
                    screenWidth < 820
                        ? Visibility(
                            visible: !isOnlineSelected.value,
                            child: Column(
                              children: [
                                //_buildClassCity(),
                                buildclassAddress()
                              ],
                            ))
                        : Visibility(
                            visible: !isOnlineSelected.value,
                            child: Row(
                              children: [
                                // Expanded(
                                //   child: _buildClassCity(),
                                // ),
                                Expanded(child: buildclassAddress()),
                              ],
                            ),
                          ),
                    screenWidth <= 820
                        ? _buildClassSession()
                        : Row(
                            children: [
                              Expanded(child: _buildClassSession()),
                              const Spacer()
                            ],
                          ),
                    const Divider(thickness: 1.0, color: AppColors.kgrey),
                    screenWidth <= 820
                        ? Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildclassFee(),
                              if (!isTicketFree.value)
                                SizedBox(
                                    width: Get.width * .5,
                                    child: _buildClassPrice())
                            ],
                          )
                        : Row(
                            children: [
                              Expanded(child: _buildclassFee()),
                              const Gap(30),
                              isTicketFree.value
                                  ? const Spacer()
                                  : Expanded(child: _buildClassPrice())
                            ],
                          ),
                    const Gap(10),
                    const Divider(thickness: 1.0, color: AppColors.kgrey),
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("Contact number* (booking and enquiry)",
                              style: body2TextRegular),
                          const Gap(8),
                          Row(
                            children: [
                              Expanded(
                                child: CustomTextFormField(
                                  // initialValue: classVM
                                  //         ._createClassModel!.ctamobile ??
                                  //     "",
                                  onChanged: (val) {
                                    _createClassModel!.ctamobile = "+91$val";
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                    LengthLimitingTextInputFormatter(10),
                                  ],
                                  hintText: "9001122445",
                                  prefixIcon: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 16),
                                    child: Text(
                                      "+91",
                                      style: body2TextMedium.copyWith(
                                          color: AppColors.txtprimary),
                                    ),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Contact Required';
                                    }
                                    if (value.length != 10) {
                                      return 'Mobile number must be 10 digits';
                                    }
                                    if (!RegExp(r'^[6-9][0-9]{9}$')
                                        .hasMatch(value)) {
                                      return 'Please enter a valid mobile number';
                                    }
                                    return null;
                                  },
                                  maxLength: 10,
                                ),
                              ),
                              const Gap(10),
                              const Spacer(),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const Divider(thickness: 1.0, color: AppColors.kgrey),
                    screenWidth <= 820
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Padding(
                                padding: EdgeInsets.all(16),
                                // ignore: unnecessary_const
                                child: const Row(
                                  children: [
                                    Icon(Icons.info_outline),
                                    Gap(8),
                                    Expanded(
                                      child: Text(
                                        "Please fill out all required fields to submit for review",
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Gap(15),
                              buildSubmitButton(),
                              const Gap(25),
                            ],
                          )
                        : Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Row(
                                  children: [
                                    Icon(Icons.info_outline),
                                    Gap(8),
                                    Text(
                                      "Please fill out all required fields to submit for review.",
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                                buildSubmitButton(),
                              ],
                            ))
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClassNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text('Class title (max.50 char)*', style: body2TextRegular),
        const Gap(10),
        CustomTextFormField(
          // initialValue: classVM._createClassModel!.title,
          controller: _titleController,
          hintText: 'e.g Art attack',
          maxLength: 50,
          onChanged: (val) {
            _createClassModel!.title = val;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Class Title Required';
            }
            return null;
          },
        ),
        const Gap(10),
        Align(
            alignment: Alignment.centerRight,
            child: Text(
                '${_titleController.text.length}/$_maxTitleLength characters',
                style: body3TextRegular)),
        const Gap(20),
        Text('Description (max.500 char)*', style: body2TextRegular),
        const Gap(10),
        CustomTextFormField(
          controller: _descriptionController,
          // initialValue:
          //     classVM._createClassModel!.description,
          onChanged: (val) {
            _createClassModel!.description = val;
          },
          hintText: "Type here",
          maxLines: _maxLength,
          maxLength: 500,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Class Desc. Required';
            }
            return null;
          },
        ),
        const Gap(10),
        Align(
            alignment: Alignment.centerRight,
            child: Text(
                '${_descriptionController.text.length}/$_maxLength characters',
                style: body3TextRegular)),
      ],
    );
  }

  Widget _buildClassBannerField() {
    double screenWidth = MediaQuery.of(context).size.width;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Class banner*", style: body2TextRegular),
        const Gap(10),
        _imageUrl == null
            ? DottedBorder(
                color: AppColors.bordergrey,
                strokeWidth: 1,
                dashPattern: const [6, 3],
                borderType: BorderType.RRect,
                radius: const Radius.circular(4),
                child: Container(
                    height: screenWidth <= 820 ? 200 : 260,
                    width: screenWidth <= 820 ? Get.width : null,
                    // margin:
                    //     const EdgeInsets.only(top: 10),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const Icon(Icons.add_photo_alternate_outlined,
                            size: 60, color: AppColors.kprimarycolor),
                        const Gap(10),
                        Text(
                          "JPEG or PNG file formats supported in\n1080 x 1080px dimension, up to 1 MB",
                          style: body2TextRegular.copyWith(
                              color: AppColors.txtsecondary),
                        ),
                        const Gap(15),
                        TextButton(
                          child: Text(
                            'Upload Image',
                            textAlign: TextAlign.center,
                            style: bodyTextBold.copyWith(
                              color: const Color(0xff5E57E1),
                            ),
                          ),
                          onPressed: () async {
                            log("Upload Image button pressed");
                            FilePickerResult? result =
                                await FilePicker.platform.pickFiles();

                            if (result != null) {
                              PlatformFile pickedFile = result.files.first;
                              Uint8List? fileBytes = result.files.first.bytes;
                              log("File selected: ${pickedFile.name}");
                              await _uploadFileHelper(
                                  pickedFile, null, fileBytes!);
                            } else {
                              log('No file selected.');
                            }
                          },
                        ),
                      ],
                    )),
              )
            : Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      height: 260,
                      width: screenWidth <= 820 ? Get.width : 260,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: NetworkImage(_imageUrl!),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 5,
                    right: 4,
                    // left: 0,

                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _imageUrl = "";
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: AppColors.kprimarycolor,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: const Icon(
                          Icons.edit,
                          size: 20,
                          color: AppColors.kwhite,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ],
    );
  }

  Widget buildclassAgeGroup() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Suitable age group*", style: body2TextRegular),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: CustomDropdownFormField(
                  items: _ageOptions.map((age) {
                    return DropdownMenuItem(
                      value: age,
                      child: Text(age),
                    );
                  }).toList(),
                  value: _selectedMinAge,
                  hintText: 'Min.age',
                  onChanged: (value) {
                    setState(() {
                      _selectedMinAge = value;
                      _createClassModel!.minAge = int.parse(value!);
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select Min Age';
                    }
                    return null;
                  },
                ),
              ),
              const Gap(10),
              Text(
                'To',
                style: body3TextRegular.copyWith(color: AppColors.txtsecondary),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: CustomDropdownFormField(
                  items: _maxAgeOptions.map((age) {
                    return DropdownMenuItem(
                      value: age,
                      child: Text(age),
                    );
                  }).toList(),
                  value: _selectedMaxAge,
                  hintText: 'Max.age',
                  onChanged: (value) {
                    setState(() {
                      _selectedMaxAge = value;

                      if (_selectedMaxAge == "18+") {
                        _createClassModel!.maxAge = 19;
                        log("Selected max age: 19");
                      } else {
                        try {
                          int selectedMaxAge = int.parse(value!);
                          int selectedMinAge = _selectedMinAge == "18+"
                              ? 18
                              : int.parse(_selectedMinAge!);

                          if (selectedMaxAge <= selectedMinAge) {
                            Get.snackbar(
                              "Error",
                              "The maximum age should be greater than the minimum age.",
                              snackStyle: SnackStyle.FLOATING,
                              backgroundColor: AppColors.errorRed,
                              maxWidth: 300,
                              colorText: AppColors.kwhite,
                            );
                          } else {
                            _createClassModel!.maxAge = selectedMaxAge;
                            log("Selected max age: ${_createClassModel!.maxAge}");
                          }
                        } catch (e) {
                          log("Error parsing age value: $e");
                          Get.snackbar(
                            "Error",
                            "Invalid age value.",
                            snackStyle: SnackStyle.FLOATING,
                            backgroundColor: AppColors.errorRed,
                            maxWidth: 300,
                            colorText: AppColors.kwhite,
                          );
                        }
                      }
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Max Age Required';
                    } else if (_selectedMinAge == "18+" && value == "18+") {
                      return null;
                    } else if (value == "18+") {
                      return null;
                    } else {
                      try {
                        int selectedMaxAge = int.parse(value);
                        int selectedMinAge = _selectedMinAge == "18+"
                            ? 18
                            : int.parse(_selectedMinAge!);

                        if (selectedMaxAge <= selectedMinAge) {
                          return "The maximum age should be greater than the minimum age.";
                        }
                      } catch (e) {
                        return "Invalid age value.";
                      }
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildclassType() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Class type*", style: body2TextRegular),
          const Gap(8),
          Row(
            children: [
              Flexible(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isOfflineSelected.value = true;
                      isOnlineSelected.value = false;
                      isOfflineOnlineSelected.value = false;
                      _createClassModel!.classType = "offline";

                      // if (selectedAddress.isEmpty) {
                      //   _initializeDefaultAddress();
                      // }
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(8),
                        right: Radius.zero,
                      ),
                      color: isOfflineSelected.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isOfflineSelected.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text("Offline",
                          style: body2TextSemiBold.copyWith(
                              color: isOfflineOnlineSelected.value
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
              Flexible(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isOfflineSelected.value = false;
                      isOnlineSelected.value = true;
                      isOfflineOnlineSelected.value = false;
                      _createClassModel!.classType = "online";

                      selectedAddress = '';
                      _createClassModel!.address = '';
                      _createClassModel!.city = '';
                      _createClassModel!.locationDetails = LocationDetails();
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                        color: isOnlineSelected.value
                            ? AppColors.kwhite
                            : AppColors.scaffoldColor,
                        border: Border.all(
                          color: isOnlineSelected.value
                              ? AppColors.txtprimary
                              : AppColors.bordergrey,
                        )),
                    child: Center(
                      child: Text("Online",
                          style: body2TextSemiBold.copyWith(
                              color: isOnlineSelected.value
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
              Flexible(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isOfflineSelected.value = false;
                      isOnlineSelected.value = false;
                      isOfflineOnlineSelected.value = true;
                      _createClassModel!.classType = "offline + online";
                      // if (selectedAddress.isEmpty) {
                      //   _initializeDefaultAddress();
                      // }
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(8),
                      ),
                      color: isOfflineOnlineSelected.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                          color: isOfflineOnlineSelected.value
                              ? AppColors.txtprimary
                              : AppColors.bordergrey),
                    ),
                    child: Center(
                      child: Text("Offline + Online",
                          style: body2TextSemiBold.copyWith(
                              color: isOfflineOnlineSelected.value
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Widget _buildClassCity() {
  //   return Padding(
  //     padding: const EdgeInsets.all(16),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         //   CustomDropdownUI(
  //         //   isValidatorReq: true,
  //         //   title: "City",
  //         //   items: classVM.serviceableCity
  //         //       .map((city) {
  //         //     return DropdownMenuItem(
  //         //       onTap: () {
  //         //         _createClassModel!.city =
  //         //             city;

  //         //       },
  //         //       value: city,
  //         //       child: Text(
  //         //         city,
  //         //         style: body2TextBold,
  //         //       ),
  //         //     );
  //         //   }).toList(),
  //         //   value: selectedCity,
  //         //   onChanged: (value) {
  //         //     setState(() {
  //         //       selectedCity = value.toString();
  //         //     });
  //         //   },
  //         // ),
  //         Text("City*", style: body2TextRegular),
  //         const Gap(10),
  //         CustomDropDown(
  //           items: classVM.serviceableCity,
  //           hinttext: "Select",
  //           onChanged: (value) {
  //             _createClassModel!.city = value!;
  //           },
  //           initialValue: null,
  //           onSaved: (p0) {},
  //           onTap: () {},
  //           validator: (value) {
  //             if (value == null || value.isEmpty) {
  //               return 'City Required';
  //             }
  //             return null;
  //           },
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget buildclassAddress() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomDropdownUI(
            isValidatorReq: true,
            title: "Address",
            items: [
              ...() {
                final uniqueAddresses = <String>{};
                return businessController.userModel.value.location
                    .where((location) =>
                        location.address.isNotEmpty &&
                        uniqueAddresses.add(location.address))
                    .map((location) {
                  return DropdownMenuItem(
                    value: location.address,
                    child: Text(
                      location.address.toString(),
                      style: body2TextBold,
                    ),
                  );
                }).toList();
              }(),
              DropdownMenuItem(
                value: 'add_new_address',
                child: Row(
                  children: [
                    Icon(
                      Icons.add,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Add new address',
                      style: bodyTextMedium.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            value: selectedAddress == '' ? null : selectedAddress,
            onChanged: (value) {
              if (value == 'add_new_address') {
                FocusScope.of(context).unfocus();
                // Navigate after a small delay to ensure dropdown closes
                Future.delayed(const Duration(milliseconds: 100), () {
                  locator<NavigationServices>()
                      .navigateTo(
                    addAddressPage,
                    arguments: const ProfileAddAddress(isEditing: false),
                  )
                      .then((result) async {
                    await businessController.businessProfileDetails();
                    businessController.userModel.refresh();
                    if (businessController
                        .userModel.value.location.isNotEmpty) {
                      // Select the last added address (assuming it's the newest)
                      var lastLocation =
                          businessController.userModel.value.location.last;
                      selectedAddress = lastLocation.address;
                      var locationDetails = LocationDetails(
                        area: lastLocation.area,
                        city: lastLocation.city,
                        state: lastLocation.state,
                        address: lastLocation.address,
                        country: lastLocation.country,
                        latitude: double.parse(lastLocation.latitude),
                        pinCode: lastLocation.pinCode,
                        longitude: double.parse(lastLocation.longitude),
                        locationId: lastLocation.locationId,
                        subLocality: lastLocation.subLocality,
                      );

                      _createClassModel!.city = lastLocation.city;
                      _createClassModel!.locationDetails = locationDetails;
                      eventController.eventDetailsModel.value.address =
                          lastLocation.address;
                    } else {
                      selectedAddress = '';
                    }
                    if (mounted) {
                      setState(() {});
                    }
                  });
                });
                return;
              }
              if (value == 'separator') {
                // Ignore separator selection
                return;
              }
              var location = businessController.userModel.value.location
                  .firstWhere((location) => location.address == value);
              var locationDetails = LocationDetails(
                  area: location.area,
                  city: location.city,
                  state: location.state,
                  address: location.address,
                  country: location.country,
                  latitude: double.parse(location.latitude),
                  pinCode: location.pinCode,
                  longitude: double.parse(location.longitude),
                  locationId: location.locationId,
                  subLocality: location.subLocality);
              _createClassModel!.city = location.city;
              _createClassModel!.locationDetails = locationDetails;
              _createClassModel!.address = location.address;

              setState(() {
                selectedAddress = value.toString();
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildClassSession() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Session type*", style: body2TextRegular),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isGroupSession.value = true;
                      is1Session.value = false;
                      isGroup1Session.value = false;
                      _createClassModel!.sessionType = "Group";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(8),
                        right: Radius.zero,
                      ),
                      color: isGroupSession.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isGroupSession.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Group",
                        style: body2TextSemiBold.copyWith(
                          color: isGroupSession.value
                              ? AppColors.txtprimary
                              : AppColors.txtsecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isGroupSession.value = false;
                      is1Session.value = true;
                      isGroup1Session.value = false;
                      _createClassModel!.sessionType = "1:1";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      color: is1Session.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: is1Session.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "1:1",
                        style: body2TextSemiBold.copyWith(
                          color: is1Session.value
                              ? AppColors.txtprimary
                              : AppColors.txtsecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isGroupSession.value = false;
                      is1Session.value = false;
                      isGroup1Session.value = true;
                      _createClassModel!.sessionType = "Group + 1:1";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(8),
                      ),
                      color: isGroup1Session.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isGroup1Session.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Group + 1:1",
                        style: body2TextSemiBold.copyWith(
                          color: isGroup1Session.value
                              ? AppColors.txtprimary
                              : AppColors.txtsecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildclassFee() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Class Fee*", style: body2TextRegular),
          const Gap(8),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isTicketPaid.value = true;
                      isTicketFree.value = false;
                      _createClassModel!.classFee = "Paid";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(12),
                      ),
                      color: isTicketPaid.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isTicketPaid.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Paid",
                        style: body2TextSemiBold.copyWith(
                            color: isTicketPaid.value
                                ? AppColors.txtprimary
                                : AppColors.txtsecondary),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isTicketPaid.value = false;
                      isTicketFree.value = true;
                      _createClassModel!.classFee = "Free";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(12),
                      ),
                      color: isTicketFree.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isTicketFree.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Free",
                        style: body2TextSemiBold.copyWith(
                            color: isTicketFree.value
                                ? AppColors.txtprimary
                                : AppColors.txtsecondary),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildClassPrice() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Amount", style: body2TextRegular),
          CustomTextFormField(
            // initialValue: classVM
            //     ._createClassModel!.price
            //     .toString(),

            onChanged: (val) {
              int price = int.tryParse(val) ?? 0;
              _createClassModel!.price = price;
            },
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            maxLength: 8,
            hintText: "Fees per month",
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Amount Required';
              }
              return null;
            },
            prefixIcon: const Padding(
              padding: EdgeInsets.only(left: 8),
              child: Icon(Icons.currency_rupee,
                  size: 20, color: AppColors.txtsecondary),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildSubmitButton() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Row(
      mainAxisAlignment:
          screenWidth <= 820 ? MainAxisAlignment.center : MainAxisAlignment.end,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: SecondaryButton(
            textColor:
                // : businessController.userModel.value.kycDone == 0
                //     ? AppColors.ktertiary
                //     :
                AppColors.kprimarycolor,
            backgroundColor:
                // businessController.userModel.value.kycDone == 0
                //     ? AppColors.scaffoldColor
                //     :
                AppColors.kwhite,
            text: 'Save as draft',
            onTap: () async {
              // if (businessController.userModel.value.kycDone == 1) {
              onSubmitOrSaveDraft(false);
              // }
            },
          ),
        ),
        const Gap(20),
        PrimaryButton(
            textColor:
                // businessController.userModel.value.kycDone == 0
                //     ? AppColors.ktertiary
                //     :
                AppColors.kwhite,
            backgroundColor:
                //  businessController.userModel.value.kycDone == 0
                //     ? AppColors.scaffoldColor
                //     :
                AppColors.kprimarycolor,
            text: 'Submit for review',
            onTap: () async {
              // if (businessController.userModel.value.kycDone == 1) {
              if (kDebugMode) {
                print('anuj class payload:${json.encode(_createClassModel)}');
              }

              onSubmitOrSaveDraft(true);
            }
            // },
            ),
      ],
    );
  }

  void onSubmitOrSaveDraft(bool isSubmit) async {
    if (_createClassModel!.classType == "offline" ||
        _createClassModel!.classType == "offline + online") {
      if (selectedAddress.isEmpty) {
        Get.snackbar(
          "Error",
          "All field is required.",
          snackStyle: SnackStyle.FLOATING,
          backgroundColor: AppColors.errorRed,
          maxWidth: 300,
          colorText: AppColors.kwhite,
        );
        return;
      }
    }

    if (_formKey.currentState?.validate() ?? false) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return ConfirmPopup(
            dialogHeight: isSubmit ? 220 : 180,
            dialogWidth: 300,
            title: isSubmit ? 'Submit for review' : 'Save as draft',
            message: isSubmit
                ? 'Please ensure the details you entered are\ncorrect. Once submitted, your event will be\nreviewed and published upon approval.'
                : 'Are you sure you want to save this as a draft?',
            onConfirm: () async {
              log(isSubmit ? "Submit for review" : "Save as draft");
              if (kDebugMode) {
                print(
                    'Session type before API call: ${_createClassModel!.sessionType}');
              }
              bool isSuccess = await classVM.createClass(
                  publish: isSubmit ? 1 : 0,
                  status: isSubmit ? "in-review" : "draft",
                  createClassModel: _createClassModel!);
              if (isSuccess) {
                await Future.delayed(const Duration(milliseconds: 300))
                    .then((value) {
                  Navigator.pop(Get.context!);
                  Get.snackbar(
                    "Success",
                    isSubmit ? "Class submited for review." : "Saved as draft.",
                    snackStyle: SnackStyle.FLOATING,
                    backgroundColor: AppColors.kprimarycolor,
                    maxWidth: 300,
                    colorText: AppColors.kwhite,
                  );
                  String targetTab = isSubmit ? "published" : "draft";
                  locator<NavigationServices>().goBackWithResult(targetTab);
                });
              } else {
                Get.snackbar(
                  "Failed",
                  isSubmit
                      ? "Failed to submit for review. Please try again.."
                      : "Failed to save as draft. Please try again.",
                  snackStyle: SnackStyle.FLOATING,
                  backgroundColor: AppColors.errorRed,
                  maxWidth: 300,
                  colorText: AppColors.kwhite,
                );
              }
            },
            icon: SvgPicture.asset(
              isSubmit
                  ? 'assets/icons/Checks.svg'
                  : 'assets/icons/PencilLine.svg',
              height: 50,
              fit: BoxFit.fill,
            ),
            confirmText: isSubmit ? 'Submit' : 'Save as draft',
            cancelText: 'Cancel',
          );
        },
      );
    } else {
      Get.snackbar(
        "Error",
        "All field is required.",
        snackStyle: SnackStyle.FLOATING,
        backgroundColor: AppColors.errorRed,
        maxWidth: 300,
        colorText: AppColors.kwhite,
      );
    }
  }

  Future<void> _uploadFileHelper(
      PlatformFile pickedFile, File? file, Uint8List fileBytes) async {
    String fileExtension = pickedFile.extension?.toLowerCase() ?? '';
    List<String> allowedExtensions = ['jpg', 'jpeg', 'png'];

    if (!allowedExtensions.contains(fileExtension)) {
      Get.snackbar(
        "Error",
        "Please upload a valid image file (jpg, jpeg, png).",
        snackStyle: SnackStyle.FLOATING,
        backgroundColor: AppColors.errorRed,
        maxWidth: 300,
        colorText: AppColors.kwhite,
      );
      return;
    }

    if (pickedFile.size > 1 * 1024 * 1024) {
      Get.snackbar(
        "Error",
        "File size exceeds 1 MB limit.",
        snackStyle: SnackStyle.FLOATING,
        backgroundColor: AppColors.errorRed,
        maxWidth: 300,
        colorText: AppColors.kwhite,
      );
      return;
    }

    String contentType = 'image/$fileExtension';
    String filePath = file?.path ?? '';
    log("Starting _uploadFileHelper with fileName: ${pickedFile.name}, filePath: $filePath");
    bool value = await eventController.createFileNameEntry(
        pickedFile.name, contentType, filePath, fileBytes, "banner");
    if (value) {
      String encodedFileName = Uri.encodeComponent(pickedFile.name);
      String newImageUrl =
          "https://profilemedia.s3.ap-south-1.amazonaws.com/$encodedFileName";
      setState(() {
        eventController.uploadedFileName.value = pickedFile.name;
        _imageUrl = newImageUrl;
        _createClassModel!.bannerUrl = newImageUrl;
      });
      log("_imageUrl set to: $newImageUrl");
      Get.snackbar(
        "Succes",
        "Class Banner uploaded successfully",
        snackStyle: SnackStyle.FLOATING,
        backgroundColor: AppColors.kprimarycolor,
        maxWidth: 300,
        colorText: AppColors.kwhite,
      );
    } else {
      log("unable to upload the image");
      Get.snackbar(
        "Failed",
        "Unable to upload class banner... try again later",
        snackStyle: SnackStyle.FLOATING,
        backgroundColor: AppColors.errorRed,
        maxWidth: 300,
        colorText: AppColors.kwhite,
      );
    }
  }
}
