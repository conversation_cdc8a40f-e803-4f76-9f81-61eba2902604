import 'dart:async';
import 'package:flutter/material.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_root/admin_desktop_page.dart';
import 'package:parenthing_dashboard/res/constants/screen_res.dart';
import 'package:parenthing_dashboard/view/root/mobile_view_page.dart';

class AdminLoadingAnimationPage extends StatefulWidget {
  const AdminLoadingAnimationPage({super.key});

  @override
  State<AdminLoadingAnimationPage> createState() =>
      _AdminLoadingAnimationPageState();
}

class _AdminLoadingAnimationPageState extends State<AdminLoadingAnimationPage> {
  @override
  void initState() {
    super.initState();
    startTimer();
  }

  startTimer() async {
    var duration = const Duration(seconds: 3);
    return Timer(duration, route);
  }

  route() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => const ResponsiveWidget(
          largeScreen: AdminDesktopViewPage(),
          mediumScreen: AdminDesktopViewPage(),
          smallScreen: MobileViewPage(),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator.adaptive(),
    );
  }
}
