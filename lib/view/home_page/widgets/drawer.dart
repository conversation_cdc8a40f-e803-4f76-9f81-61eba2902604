import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/value_notifier.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/sidebar_button.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/common_widgets/logout_dialog.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:parenthing_dashboard/view/loginPage/loginpage.dart';
import '../../../routing/locator.dart';
import 'dart:html' as html;

class DrawerPage extends StatefulWidget {
  const DrawerPage({super.key});

  @override
  State<DrawerPage> createState() => _DrawerPageState();
}

class _DrawerPageState extends State<DrawerPage> {
  final BusinessController businessVM = Get.find<BusinessController>();
  final UserController userControllerVM = Get.find<UserController>();
  final ApiController apiController = Get.find<ApiController>();
  final AdminUserController adminUserController =
      Get.find<AdminUserController>();

  @override
  void initState() {
    super.initState();
    selectedBusinessPageNotifier.addListener(_updateSelectedPage);
  }

  @override
  void dispose() {
    selectedBusinessPageNotifier.removeListener(_updateSelectedPage);
    super.dispose();
  }

  void _updateSelectedPage() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return Container(
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(color: AppColors.ktertiary.withOpacity(.3)),
          right: BorderSide(color: AppColors.ktertiary.withOpacity(.3)),
          bottom: BorderSide(color: AppColors.ktertiary.withOpacity(.3)),
        ),
      ),
      child: Drawer(
        width: screenWidth <= 770 ? 150 : 200,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        elevation: 1.0,
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  kSmHeight,
                  // SideBardButton(
                  //   buttonText: 'Notifications',
                  //   buttonIcon: 'assets/svg/Bell.svg',
                  //   selectedIcon: "assets/svg/Bell.svg",
                  //   isSelected:
                  //       selectedBusinessPageNotifier.value == "Notifications",
                  //   onClick: () {
                  //     Future.microtask(() {
                  //       _updateSelectedPage();
                  //       locator<NavigationServices>()
                  //           .navigateTo(businessNotificationsPage);
                  //       selectedBusinessPageNotifier.value = "Notifications";
                  //     });
                  //   },
                  // ),
                  // const Padding(
                  //   padding: EdgeInsets.all(8.0),
                  //   child: Divider(
                  //     height: 1.0,
                  //     color: AppColors.detailgrey,
                  //   ),
                  // ),
                  // SideBardButton(
                  //   buttonText: 'Home',
                  //   buttonIcon: 'assets/icons/Vector.svg',
                  //   selectedIcon: "assets/icons/admin_home_Active.svg",
                  //   isSelected: selectedBusinessPageNotifier.value == "Home",
                  //   onClick: () {
                  //     Future.microtask(() {
                  //       _updateSelectedPage();
                  //       locator<NavigationServices>().navigateTo(homeRoute);
                  //       selectedBusinessPageNotifier.value = "Home";
                  //     });
                  //   },
                  // ),
                  Center(
                    child: SideBardButton(
                      buttonText: 'Events',
                      buttonIcon: 'assets/icons/Event.svg',
                      selectedIcon: "assets/icons/Event__Active.svg",
                      isSelected:
                          selectedBusinessPageNotifier.value == "Events",
                      onClick: () {
                        Future.microtask(() {
                          // _updateSelectedPage();
                          selectedBusinessPageNotifier.value = "Events";
                          locator<NavigationServices>().navigateTo(eventRoute);
                          _updateSelectedPage();
                        });
                      },
                    ),
                  ),
                  Center(
                    child: SideBardButton(
                      buttonText: 'Classes',
                      buttonIcon: 'assets/icons/Classes.svg',
                      selectedIcon: "assets/icons/Classes_Active.svg",
                      isSelected:
                          selectedBusinessPageNotifier.value == "Classes",
                      onClick: () {
                        Future.microtask(() {
                          selectedBusinessPageNotifier.value = "Classes";
                          locator<NavigationServices>()
                              .navigateTo(classesRoute);
                          _updateSelectedPage();
                        });
                      },
                    ),
                  ),
                  Center(
                    child: SideBardButton(
                      buttonText: 'Profile',
                      buttonIcon: 'assets/icons/User.svg',
                      selectedIcon: "assets/icons/User_Active.svg",
                      isSelected:
                          selectedBusinessPageNotifier.value == "Profile",
                      onClick: () {
                        Future.microtask(() {
                          selectedBusinessPageNotifier.value = 'Profile';
                          locator<NavigationServices>()
                              .navigateTo(profileRoute);
                          _updateSelectedPage();
                        });
                      },
                    ),
                  ),
                  Center(
                    child: SideBardButton(
                      buttonText: 'Help',
                      buttonIcon: 'assets/icons/business_help_active.svg',
                      selectedIcon: "assets/icons/business_help.svg",
                      isSelected: selectedBusinessPageNotifier.value == "Help",
                      onClick: () {
                        Future.microtask(() {
                          selectedBusinessPageNotifier.value = 'Help';
                          locator<NavigationServices>().navigateTo(helpRoute);
                          _updateSelectedPage();
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(children: [
                // const Divider(
                //   height: 1.0,
                //   color: AppColors.detailgrey,
                // ),
                // kSmHeight,
                SideBardButton(
                  buttonText: 'Logout',
                  buttonIcon: 'assets/icons/logout.svg',
                  isSelected: selectedBusinessPageNotifier.value == 'Logout',
                  selectedIcon: "",
                  onClick: () {
                    _showLogoutDialog();
                  },
                ),
              ]),
            ),
            kSmHeight,
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CustomDialog(
          onConfirmTxt: "Log out",
          onCancelText: "Cancel",
          title: 'Log out?',
          content: 'You will be returned to the login screen.',
          image: "assets/icons/SignOut.svg",
          onConfirm: () {
            Navigator.pop(context);
            _performLogout();
          },
          onCancel: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  void _performLogout() async {
    selectedBusinessPageNotifier.removeListener(_updateSelectedPage);
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // Perform logout operations
      await _fastLogout();

      // Close loading dialog
      if (Navigator.canPop(Get.context!)) {
        Navigator.pop(Get.context!);
      }

      // Navigate to login
      Get.offAll(() => const LoginPage());
    } catch (e) {
      log('Logout error: $e');

      // Close loading dialog
      if (Navigator.canPop(Get.context!)) {
        Navigator.pop(Get.context!);
      }

      // Fallback logout
      _emergencyLogout();
    }
  }

  Future<void> _fastLogout() async {
    // Remove device token (non-blocking)
    userControllerVM.removeSavedDeviceToken();

    // Clear all storage in parallel
    await Future.wait([
      storage.erase(),
      _clearWebStorage(),
    ]);

    // Reset UI state
    selectedBusinessPageNotifier.value = "Events"; // Default to first page

    // Small delay for cleanup
    await Future.delayed(const Duration(milliseconds: 100));
  }

  Future<void> _clearWebStorage() async {
    try {
      html.window.localStorage.clear();
      html.window.sessionStorage.clear();
    } catch (e) {
      log('Error clearing web storage: $e');
    }
  }

  void _emergencyLogout() {
    try {
      storage.erase();
      selectedBusinessPageNotifier.value = "Events";
      html.window.localStorage.clear();
      html.window.sessionStorage.clear();
    } catch (e) {
      log('Emergency logout error: $e');
    } finally {
      Get.offAll(() => const LoginPage());
    }
  }
}

//! admin Drawer Page
class AdminDrawerPage extends StatefulWidget {
  const AdminDrawerPage({super.key});

  @override
  State<AdminDrawerPage> createState() => _AdminDrawerPageState();
}

class _AdminDrawerPageState extends State<AdminDrawerPage> {
  final AdminUserController adminUserController =
      Get.find<AdminUserController>();

  @override
  void initState() {
    super.initState();
    adminSelectedPageNotifier.addListener(_updateSelectedPage);
  }

  @override
  void dispose() {
    adminSelectedPageNotifier.removeListener(_updateSelectedPage);
    super.dispose();
  }

  void _updateSelectedPage() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(color: AppColors.ktertiary.withOpacity(.3)),
          right: BorderSide(color: AppColors.ktertiary.withOpacity(.3)),
          bottom: BorderSide(color: AppColors.ktertiary.withOpacity(.3)),
        ), // Border color added
      ),
      child: Drawer(
        width: 200,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        elevation: 1.0,
        child: Column(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: ListView(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    kSmHeight,
                    SideBardButton(
                      buttonText: 'Home',
                      buttonIcon: 'assets/icons/Vector.svg',
                      selectedIcon: "assets/icons/admin_home_Active.svg",
                      isSelected: adminSelectedPageNotifier.value == "Home",
                      onClick: () {
                        // _onAdminButtonTap('Home');
                        adminSelectedPageNotifier.value = 'Home';
                        locator<NavigationServices>()
                            .navigateTo(adminHomeRoute);
                      },
                    ),
                    SideBardButton(
                      buttonText: 'Parents',
                      buttonIcon: 'assets/icons/admin_parents.svg',
                      selectedIcon: "assets/icons/admin_parents_active.svg",
                      isSelected: adminSelectedPageNotifier.value == "Parents",
                      onClick: () {
                        if (adminUserController
                                    .adminDetailsModel.value.position ==
                                "Reports" ||
                            adminUserController
                                    .adminDetailsModel.value.position ==
                                "Admin" ||
                            adminUserController
                                    .adminDetailsModel.value.position ==
                                "Super Admin") {
                          adminSelectedPageNotifier.value = 'Parents';
                          locator<NavigationServices>()
                              .navigateTo(adminParentsRoute);
                        } else {
                          Get.snackbar(
                            'Access Denied',
                            "You don't have access for parents.",
                            snackPosition: SnackPosition.TOP,
                            backgroundColor: AppColors.errorRed,
                            maxWidth: 300,
                            colorText: AppColors.kwhite,
                          );
                        }
                      },
                    ),
                    SideBardButton(
                        buttonText: 'Business',
                        buttonIcon: 'assets/icons/admin_business.svg',
                        selectedIcon: "assets/icons/admin_business_active.svg",
                        isSelected:
                            adminSelectedPageNotifier.value == "Business",
                        onClick: () {
                          final userPosition = adminUserController
                              .adminDetailsModel.value.position;
                          if (userPosition == "Moderator" ||
                              userPosition == "Creator") {
                            Get.snackbar(
                              'Access Denied',
                              "You don't have access for this.",
                              snackPosition: SnackPosition.TOP,
                              backgroundColor: AppColors.errorRed,
                              maxWidth: 300,
                              colorText: AppColors.kwhite,
                            );
                          } else {
                            adminSelectedPageNotifier.value = 'Business';
                            int initialIndex = 0;
                            switch (userPosition) {
                              case "KYC":
                                initialIndex = 1;
                                break;
                              case "Events":
                                initialIndex = 2;
                                break;
                              case "Classes":
                                initialIndex = 3;
                                break;
                              case "Reports":
                                initialIndex = 4;
                                break;
                            }
                            locator<NavigationServices>().navigateTo(
                              adminBusinessRoute,
                              arguments: {'initialIndex': initialIndex},
                            );
                          }
                        }),
                    SideBardButton(
                      buttonText: 'Analytics',
                      buttonIcon: 'assets/icons/admin_analytics.svg',
                      selectedIcon: "",
                      isSelected:
                          adminSelectedPageNotifier.value == "Analytics",
                      onClick: () {
                        if (adminUserController
                                    .adminDetailsModel.value.position ==
                                "Super Admin" ||
                            adminUserController
                                    .adminDetailsModel.value.position ==
                                "Admin" ||
                            adminUserController
                                    .adminDetailsModel.value.position ==
                                "Moderator") {
                          // _onAdminButtonTap('Analytics');
                          adminSelectedPageNotifier.value = "Analytics";
                          locator<NavigationServices>()
                              .navigateTo(adminAnalyticsRoute);
                        } else {
                          Get.snackbar(
                            'Access Denied',
                            "You don't have access for analytics.",
                            snackPosition: SnackPosition.TOP,
                            backgroundColor: AppColors.errorRed,
                            maxWidth: 300,
                            colorText: AppColors.kwhite,
                          );
                        }
                      },
                    ),
                    SideBardButton(
                      buttonText: 'Users',
                      buttonIcon: 'assets/icons/admin_user.svg',
                      selectedIcon: "assets/icons/admin_active_user.svg",
                      isSelected: adminSelectedPageNotifier.value == "User",
                      onClick: () {
                        if (adminUserController
                                .adminDetailsModel.value.position ==
                            "Super Admin") {
                          // _onAdminButtonTap('User');
                          adminSelectedPageNotifier.value = 'User';
                          locator<NavigationServices>()
                              .navigateTo(adminUsersRoute);
                        } else {
                          Get.snackbar(
                            'Access Denied',
                            "You don't have access for users.",
                            snackPosition: SnackPosition.TOP,
                            backgroundColor: AppColors.errorRed,
                            maxWidth: 300,
                            colorText: AppColors.kwhite,
                          );
                        }
                      },
                    ),
                    SideBardButton(
                      buttonText: 'Settings',
                      buttonIcon: 'assets/icons/admin_settings.svg',
                      selectedIcon: "assets/icons/admin_active_settings.svg",
                      isSelected: adminSelectedPageNotifier.value == "Settings",
                      onClick: () {
                        if (adminUserController
                                    .adminDetailsModel.value.position ==
                                "Admin" ||
                            adminUserController
                                    .adminDetailsModel.value.position ==
                                "Moderator" ||
                            adminUserController
                                    .adminDetailsModel.value.position ==
                                "Super Admin") {
                          // _onAdminButtonTap('Settings');
                          adminSelectedPageNotifier.value = 'Settings';
                          locator<NavigationServices>()
                              .navigateTo(adminProfileRoute);
                        } else {
                          Get.snackbar(
                            'Access Denied',
                            "You don't have access for settings.",
                            snackPosition: SnackPosition.TOP,
                            backgroundColor: AppColors.errorRed,
                            maxWidth: 300,
                            colorText: AppColors.kwhite,
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(children: [
                // Divider(
                //   height: 1.0,
                //   color: AppColors.ktertiary.withOpacity(.3),
                // ),
                kSmHeight,
                SideBardButton(
                  buttonText: 'Logout',
                  buttonIcon: 'assets/icons/logout.svg',
                  selectedIcon: "",
                  isSelected: adminSelectedPageNotifier.value == 'Logout',
                  onClick: () {
                    _showAdminLogoutDialog();
                  },
                ),
              ]),
            ),
            kSmHeight,
          ],
        ),
      ),
    );
  }

  void _showAdminLogoutDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return LogoutDialog(
          onTap: () {
            Navigator.pop(context);
            _performAdminLogout();
          },
        );
      },
    );
  }

  void _performAdminLogout() async {
    adminSelectedPageNotifier.removeListener(_updateSelectedPage);
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      await Future.wait([
        storage.erase(),
        _clearWebStorage(),
      ]);

      adminSelectedPageNotifier.value = 'Home';
      if (Navigator.canPop(Get.context!)) {
        Navigator.pop(Get.context!);
      }

      Future.delayed(const Duration(milliseconds: 100), () {
        Get.offAll(() => const LoginPage());
      });
    } catch (e) {
      log('Admin logout error: $e');

      // Close loading
      if (Navigator.canPop(Get.context!)) {
        Navigator.pop(Get.context!);
      }

      // Fallback
      _emergencyAdminLogout();
    }
  }

  Future<void> _clearWebStorage() async {
    try {
      html.window.localStorage.clear();
      html.window.sessionStorage.clear();
    } catch (e) {
      log('Error clearing web storage: $e');
    }
  }

  void _emergencyAdminLogout() {
    try {
      storage.erase();
      adminSelectedPageNotifier.value = 'Home';
      html.window.localStorage.clear();
      html.window.sessionStorage.clear();
    } catch (e) {
      log('Emergency admin logout error: $e');
    } finally {
      Future.delayed(const Duration(milliseconds: 100), () {
        Get.offAll(() => const LoginPage());
      });
    }
  }
}
