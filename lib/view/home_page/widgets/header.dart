import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/value_notifier.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:parenthing_dashboard/view/common_widgets/notifications/notifications_page.dart';

class HeaderWidget extends StatefulWidget {
  const HeaderWidget({super.key});

  @override
  State<HeaderWidget> createState() => _HeaderWidgetState();
}

class _HeaderWidgetState extends State<HeaderWidget> {
final BusinessController businessController = Get.find<BusinessController>();


  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        decoration: BoxDecoration(
          color: AppColors.kwhite,
          border: Border.all(color: AppColors.ktertiary.withOpacity(.3)),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
        child: Row(
          children: [
            InkWell(
              onTap: () {
                locator<NavigationServices>().navigateTo(profileRoute);
                selectedBusinessPageNotifier.value = 'Profile';
              },
              child: Image.asset(
                "assets/png/app_logo.png",
                height: 30,
              ),
            ),
            const Spacer(),
            InkWell(
                onTap: () {
                  locator<NavigationServices>().navigateTo(profileRoute);
                  selectedBusinessPageNotifier.value = 'Profile';
                },
                child: Row(
                  children: [
                    const NotificationDropdown(),
                    // InkWell(
                    //   onTap: () {
                    //     Future.microtask(() {
                    //       locator<NavigationServices>()
                    //           .navigateTo(businessNotificationsPage);
                    //       selectedBusinessPageNotifier.value = "Notifications";
                    //     });
                    //   },
                    //   child: SvgPicture.asset("assets/svg/Bell.svg",
                    //       height: 30, width: 30, fit: BoxFit.cover),
                    // ),
                    // Text(businessController.userName.value,
                    //     style: body2TextMedium),
                    const Gap(15),
                    ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: businessController.userProfilePic.value,
                        placeholder: (context, url) => SvgPicture.asset(
                          "assets/icons/logo.svg",
                          height: 40,
                          width: 40,
                          fit: BoxFit.cover,
                        ),
                        errorWidget: (context, url, error) => SvgPicture.asset(
                          "assets/icons/logo.svg",
                          height: 40,
                          width: 40,
                          fit: BoxFit.cover,
                        ),
                        fit: BoxFit.cover,
                        width: 40.0,
                        height: 40.0,
                      ),
                    ),
                  ],
                ))
          ],
        ),
      ),
    );
  }
}

class AdminHeaderWidget extends StatefulWidget {
  const AdminHeaderWidget({super.key});

  @override
  State<AdminHeaderWidget> createState() => _AdminHeaderWidgetState();
}

class _AdminHeaderWidgetState extends State<AdminHeaderWidget> {
  final AdminUserController adminUserController = Get.find<AdminUserController>();

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        decoration: BoxDecoration(
          color: AppColors.kwhite,
          border: Border.all(color: AppColors.ktertiary.withOpacity(.3)),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
        child: Row(
          children: [
            InkWell(
              onTap: () {
                // locator<NavigationServices>().navigateTo(profileRoute);
              },
              child: Image.asset(
                "assets/png/app_logo.png",
                height: 30,
              ),
            ),
            const Spacer(),
            InkWell(
                onTap: () {
                  if (adminUserController.adminDetailsModel.value.position ==
                      "Super Admin") {
                    selectedPageNotifier.value = 'Settings';
                    locator<NavigationServices>().navigateTo(adminProfileRoute);
                  }
                  Get.snackbar(
                    'Access Denied',
                    "You don't have access to view profile.",
                    snackPosition: SnackPosition.TOP,
                    backgroundColor: AppColors.errorRed,
                    maxWidth: 300,
                    colorText: AppColors.kwhite,
                  );
                },
                child: Row(
                  children: [
                    Text(adminUserController.adminDetailsModel.value.name,
                        style: body2TextMedium),
                    const Gap(5),
                    ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: adminUserController
                            .adminDetailsModel.value.profilePictureUrl,
                        placeholder: (context, url) => SvgPicture.asset(
                          "assets/icons/logo.svg",
                          height: 50,
                          width: 50,
                          fit: BoxFit.cover,
                        ),
                        errorWidget: (context, url, error) => SvgPicture.asset(
                          "assets/icons/logo.svg",
                          height: 50,
                          width: 50,
                          fit: BoxFit.cover,
                        ),
                        fit: BoxFit.cover,
                        width: 50.0,
                        height: 50.0,
                      ),
                    ),
                  ],
                ))
          ],
        ),
      ),
    );
  }
}
