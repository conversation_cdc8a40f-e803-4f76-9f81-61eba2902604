import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/value_notifier.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';

class ProfileInComplete extends StatelessWidget {
  const ProfileInComplete({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      final isMobile = constraints.maxWidth < 600;

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.ksecondary3.withOpacity(.2),
          border: Border.all(color: AppColors.ksecondary3, width: 1.0),
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: isMobile
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      SvgPicture.asset(
                        "assets/icons/kyc_icon.svg",
                        height: 40.0,
                      ),
                      const Gap(10),
                      Expanded(
                        child: Text(
                          'Incomplete Profile',
                          style: bodyTextSemiBold.copyWith(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                  const Gap(10),
                  Text(
                    'It looks like your profile is incomplete. Please take a moment to fill in the missing details to enhance your experience on our platform.',
                    style: body3TextRegular.copyWith(fontSize: 13),
                  ),
                  const Gap(12),
                  Align(
                    alignment: Alignment.centerRight,
                    child: ElevatedButton(
                      onPressed: () {
                        log("complete button tapped");
                          final businessController = Get.find<BusinessController>();
                        locator<NavigationServices>()
                            .navigateTo(businessEditPage)
                            .then((value) {
                          businessController.businessProfileDetails();
                        });
                        selectedBusinessPageNotifier.value = 'Profile';
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.bluecolor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24.0, vertical: 10.0),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6.0),
                        ),
                      ),
                      child: Text(
                        'Complete',
                        style: rubikStyle.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppColors.kwhite,
                          fontSize: 12.0,
                        ),
                      ),
                    ),
                  )
                ],
              )
            : Row(
                children: [
                  const Gap(16),
                  SvgPicture.asset(
                    "assets/icons/kyc_icon.svg",
                    height: 55.0,
                  ),
                  const Gap(10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Incomplete Profile',
                          style: bodyTextSemiBold.copyWith(fontSize: 13),
                        ),
                        const Gap(5),
                        Text(
                          'It looks like your profile is incomplete. Please take a moment to fill in the missing details to enhance your experience on our platform.',
                          style: body3TextRegular,
                        ),
                      ],
                    ),
                  ),
                  const Gap(15),
                  Padding(
                    padding: const EdgeInsets.only(
                        top: 16.0, bottom: 15.0, right: 16.0),
                    child: InkWell(
                      onTap: () {
                        log("complete button tapped");
                          final businessController = Get.find<BusinessController>();
                        locator<NavigationServices>()
                            .navigateTo(businessEditPage)
                            .then((value) {
                          businessController.businessProfileDetails();
                        });
                        selectedBusinessPageNotifier.value = 'Profile';
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6.0),
                          color: AppColors.bluecolor,
                        ),
                        padding: const EdgeInsets.symmetric(
                            vertical: 12.0, horizontal: 30.0),
                        child: Text(
                          'Complete',
                          textAlign: TextAlign.center,
                          style: rubikStyle.copyWith(
                            fontWeight: FontWeight.w700,
                            color: AppColors.kwhite,
                            fontSize: 12.0,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      );
    });
  }
}
