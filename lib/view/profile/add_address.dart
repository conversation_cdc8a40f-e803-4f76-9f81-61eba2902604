import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:uuid/uuid.dart';
import "package:http/browser_client.dart" as browserhttp;
import 'package:http/http.dart' as http;

class ProfileAddAddress extends StatefulWidget {
  const ProfileAddAddress(
      {super.key, required this.isEditing, this.addressIndex});
  final bool isEditing;
  final int? addressIndex;

  @override
  State<ProfileAddAddress> createState() => _ProfileAddAddressState();
}

class _ProfileAddAddressState extends State<ProfileAddAddress> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final UserController userController = Get.find<UserController>();
  final ApiController apiController = Get.find<ApiController>();
final BusinessController businessController = Get.find<BusinessController>();


  String userID = "0";
  Timer? debounceTimer;
  String sessionToken = '';
  String area = '',
      city = '',
      state = '',
      address = '',
      contry = '',
      subLocality = '';
  String pinCode = '';
  double latitudeValue = 0.0, longitudeValue = 0.0;

  GoogleMapController? mapController;

  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;
  }

  LatLng? mylatlong;
  CameraPosition? cameraPosition;
  String addressTitle = '';
  List<Placemark> placemarks = [];
  String currentAddress = '';
  Map<String, dynamic> addressPayload = {};
  String searchText = '';
  String googleApiKey = "AIzaSyCSP2Se7lLMrsw2aNy4cdqHb3P0BPcZljs";
  var uuid = const Uuid();
  String _sessionToken = '122344';
  List<dynamic> placeList = [];
  int locationID = 0;
  late TextEditingController _areaController;
  late TextEditingController _titleController;
  late TextEditingController _addressController;

  @override
  void initState() {
    super.initState();
    getLocationPermission();
    _sessionToken = uuid.v4();

    if (widget.isEditing && widget.addressIndex != null) {
      var addressDetails =
          businessController.userModel.value.location[widget.addressIndex!];
      _areaController = TextEditingController(text: addressDetails.area);
      _titleController = TextEditingController(text: addressDetails.title);
      _addressController = TextEditingController(text: addressDetails.address);

      LatLng initialPosition = LatLng(double.parse(addressDetails.latitude),
          double.parse(addressDetails.longitude));
      _addMarker(initialPosition);
      cameraPosition = CameraPosition(target: initialPosition, zoom: 15);

      setState(() {
        locationID = addressDetails.locationId;
        latitudeValue = double.parse(addressDetails.latitude);
        longitudeValue = double.parse(addressDetails.longitude);
      });
      // _addMarker(LatLng(double.parse(addressDetails.latitude),
      //     double.parse(addressDetails.longitude)));
      // setState(() {
      //   locationID = addressDetails.locationId;
      // });
    } else {
      _areaController = TextEditingController();
      _titleController = TextEditingController();
      _addressController = TextEditingController();
    }
  }

  Future<Position> position = Geolocator.getCurrentPosition(
    desiredAccuracy: LocationAccuracy.high,
  );

  @override
  void dispose() {
    _areaController.dispose();
    _titleController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  final MarkerId _markerId = const MarkerId('1');

  final Map<MarkerId, Marker> _markers = {};

  void _addMarker(LatLng latLong) {
    setState(() {
      _markers.clear();
      _markers[_markerId] = Marker(
        markerId: _markerId,
        position: latLong,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (kDebugMode) {
      print("Building widget with ${placeList.length} suggestions");
    }

    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(
              color: AppColors.lgrey,
            ),
          ),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    children: [
                      InkWell(
                        onTap: () {
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return CustomDialog(
                                onConfirmTxt: "Yes, leave",
                                onCancelText: "No",
                                title: 'Leave this page?',
                                content:
                                    'Are you sure you want to leave this page? All field details will be discarded',
                                image: "assets/icons/WarningCircle.svg",
                                onConfirm: () {
                                  Navigator.of(context).pop();
                                  locator<NavigationServices>().goBack();
                                },
                                onCancel: () {
                                  Navigator.of(context).pop();
                                },
                              );
                            },
                          );
                        },
                        child: SvgPicture.asset(
                          'assets/icons/arrow-left.svg',
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text("Add address", style: heading2TextRegular),
                    ],
                  ),
                ),
                const Divider(
                  thickness: 1.0,
                  color: AppColors.kgrey,
                ),
                const Gap(10),
                screenWidth <= 820
                    ? Column(
                        children: [
                          SizedBox(
                            height: 300,
                            width: Get.width,
                            child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: buildMapContainer()),
                          ),
                          buildMapInputField()
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 400,
                            width: Get.width / 2,
                            child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: buildMapContainer()),
                          ),
                          // const Gap(30),
                          Expanded(child: buildMapInputField()),
                        ],
                      ),
                const Gap(10),
                const Divider(),
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: SizedBox(
                      width: 150,
                      child: PrimaryButton(
                        text: widget.isEditing
                            ? "Update address"
                            : "Save address",
                        onTap: () {
                          if (_formKey.currentState!.validate()) {
                            if (latitudeValue == 0.0 || longitudeValue == 0.0) {
                              _showErrorDialog(
                                  'Please select a location from the map or search for an address.');
                              return;
                            }
                            try {
                              if (widget.isEditing &&
                                  widget.addressIndex != null) {
                                // print('location edit called');
                                // print('location edit user ${int.parse(
                                //   storage.read("USER_ID"),
                                // )}');
                                //edit part
                                Map<String, dynamic> location = {
                                  "business_id": int.parse(
                                    storage.read("USER_ID"),
                                  ),
                                  "title": _titleController.text,
                                  "area": area,
                                  "city": city,
                                  "state": state,
                                  "address": _addressController.text,
                                  "country": contry,
                                  "latitude": latitudeValue,
                                  "pin_code": int.parse(pinCode),
                                  "longitude": longitudeValue,
                                  "location_id": locationID,
                                  "sub_locality": subLocality
                                };
                                // print(
                                //     'location edit payload ${json.encode(location)}');
                                userController
                                    .editAddress(location)
                                    .then((value) {
                                  businessController.businessProfileDetails();
                                  locator<NavigationServices>().goBack();
                                });
                              } else {
                                // New address
                                Map<String, dynamic> location = {
                                  "title": _titleController.text,
                                  "area": area,
                                  "city": city,
                                  "state": state,
                                  "address": _addressController.text,
                                  "country": contry,
                                  "latitude": latitudeValue,
                                  "pin_code": int.parse(pinCode),
                                  "longitude": longitudeValue,
                                  "sub_locality": subLocality
                                };
                                userController
                                    .createAddress(location)
                                    .then((value) {
                                  businessController.businessProfileDetails();
                                  locator<NavigationServices>().goBack();
                                });
                              }
                            } catch (e) {
                              _showErrorDialog(
                                  'Please select a location from the map or search for an address.');
                              log('An error occurred: ${e.toString()}');
                            }
                          } else {
                            _showErrorDialog(
                                'Please fill all required fields correctly.');
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Error'),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Widget buildMapContainer() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: GoogleMap(
            initialCameraPosition: cameraPosition ??
                const CameraPosition(
                    target: LatLng(19.2183, 72.9781), zoom: 14.0),
            onMapCreated: _onMapCreated,
            mapType: MapType.terrain,
            myLocationEnabled: true,
            markers: Set<Marker>.of(_markers.values),
            onTap: (latLong) {
              log('lat:::${latLong.latitude},${latLong.longitude}');
              _addMarker(latLong);
              _getUpdatedAddress(
                  latitude: latLong.latitude, longitude: latLong.longitude);
            },
            onCameraMove: (CameraPosition newPosition) {
              log("ON MAP");
            },
          )),
    );
  }

  Widget buildMapInputField() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text("Search location*", style: body2TextRegular),
          const Gap(10),
          CustomTextFormField(
            controller: _areaController,
            onChanged: (val) {
              setState(() {
                searchText = val;
                log('User searched: $val');
              });
              getSuggestion(val);
            },
            hintText: "Street, Area, city",
            validator: (value) {
              if (value == null ||
                  value.isEmpty ||
                  latitudeValue == 0.0 ||
                  longitudeValue == 0.0) {
                return 'Please select a location from the map or search for an address';
              }
              return null;
            },
          ),
          const Gap(30),
          if (placeList.isNotEmpty)
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    spreadRadius: 2,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: ListView.builder(
                itemCount: placeList.length,
                itemBuilder: (context, index) {
                  final prediction = placeList[index];
                  return ListTile(
                    title:
                        Text(prediction['structured_formatting']['main_text']),
                    subtitle: Text(
                        prediction['structured_formatting']['secondary_text']),
                    onTap: () async {
                      setState(() {
                        _areaController.text = prediction['description'];
                        placeList = [];
                      });
                      final placeId = prediction['place_id'];
                      final details = await getPlaceDetails(placeId);

                      if (details != null) {
                        final lat = details['geometry']['location']['lat'];
                        final lng = details['geometry']['location']['lng'];

                        LatLng newPosition = LatLng(lat, lng);
                        _addMarker(newPosition);

                        mapController?.animateCamera(
                            CameraUpdate.newLatLngZoom(newPosition, 15));

                        await _getUpdatedAddress(latitude: lat, longitude: lng);

                        _areaController.text =
                            prediction['structured_formatting']['main_text'];
                      }
                    },
                  );
                },
              ),
            ),
          Text("Address title*", style: body2TextRegular),
          const Gap(10),
          CustomTextFormField(
            controller: _titleController,
            onChanged: (val) {},
            hintText: "e.g office",
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Address title required';
              }
              return null;
            },
          ),
          const Gap(30),
          Text("Complete Address*", style: body2TextRegular),
          const Gap(10),
          CustomTextFormField(
            controller: _addressController,
            onChanged: (val) {},
            maxLines: 5,
            hintText: "Tap on map to select location",
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Complete address Required';
              }
              return null;
            },
          )
        ],
      ),
    );
  }

  Future<void> getLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(Get.context!).showSnackBar(
          const SnackBar(
            content: Text(
                'Location permissions are permanently denied. Please enable location access in your device settings.'),
          ),
        );
        return;
      }
    } catch (e, stackTrace) {
      log('error: $e \n $stackTrace');
    }
  }

  Future<void> _getUpdatedAddress(
      {required double latitude, required double longitude}) async {
    final client = browserhttp.BrowserClient();
    client.withCredentials = true;
    try {
      Map<String, dynamic> location = {
        "lat": latitude,
        "lng": longitude,
        "map_api_key": AppUrl.googleMapsAPIKEY
      };

      final value = await apiController.apiHelperFn(
          apiUrl: AppUrl.getPlaceFromMap, payload: location);
      final jsonMap = json.decode(value);
      if (jsonMap["success"] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];

          if (data.isNotEmpty) {
            final formattedAddress = data[0]['formatted_address'];
            setState(() {
              _addressController.text = formattedAddress;
              latitudeValue = data[0]['geometry']['location']['lat'];
              longitudeValue = data[0]['geometry']['location']['lng'];
            });
            final addressComponents = data[0]['address_components'];
            for (var component in addressComponents) {
              final types = component['types'];
              if (types.contains('locality')) {
                city = component['long_name'];
              } else if (types.contains('administrative_area_level_1')) {
                state = component['long_name'];
              } else if (types.contains('country')) {
                contry = component['long_name'];
              } else if (types.contains('postal_code')) {
                pinCode = component['long_name'] ?? "";
              } else if (types.contains('sublocality')) {
                subLocality = component['long_name'];
              } else if (types.contains('route')) {
                area = component['long_name'];
              }
            }
            // setState(() {});
          } else {
            if (kDebugMode) {
              print("error while loading data");
            }
            //ErrorPopup.showErrorDialog(context, "Something went wrong!");
          }
        } else {
          log("Invalid data format");
        }
      } else {
        log("API response not successful: ${value?['msg']}");
      }
    } catch (error, stackTrace) {
      if (kDebugMode) {
        print("getHomePageImages error: $error \n$stackTrace");
      }
    }
  }

  Future<void> getSuggestion(String input) async {
    if (input.isEmpty) {
      setState(() {
        placeList = [];
      });
      return;
    }

    const String apiUrl =
        'https://stag-api.parenthingapp.co.in/dashboard/business/get_address';

    try {
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          "input": input,
          "map_api_key": "AIzaSyCSP2Se7lLMrsw2aNy4cdqHb3P0BPcZljs",
          "sessionToken": _sessionToken,
        }),
      );

      log('Response status code: ${response.statusCode}');
      log(response.body);

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['success'] == true && jsonResponse['data'] != null) {
          setState(() {
            placeList = List<Map<String, dynamic>>.from(jsonResponse['data']);
            if (kDebugMode) {
              print("placelist $placeList");
            }
          });
        } else {
          setState(() {
            placeList = [];
          });
        }
      } else {
        log('location data not found');
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${response.statusCode}'),
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error making API call: $e');
      }
    }
  }

  Future<Map<String, dynamic>?> getPlaceDetails(String placeId) async {
    const String apiUrl =
        'https://stag-api.parenthingapp.co.in/dashboard/business/get_address';

    try {
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          "place_id": placeId,
          "map_api_key": AppUrl.googleMapsAPIKEY,
          "sessionToken": _sessionToken,
        }),
      );
      if (kDebugMode) {
        print("location_with_placeID:::=> ${response.body}");
      }
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['success'] == true && jsonResponse['data'] != null) {
          return jsonResponse['data'];
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching place details: $e');
      }
    }
    return null;
  }
}
