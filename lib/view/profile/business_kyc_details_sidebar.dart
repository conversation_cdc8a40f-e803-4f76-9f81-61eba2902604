import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/kyc_review.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:url_launcher/url_launcher.dart';

class BusinessKycDetails extends StatefulWidget {
  const BusinessKycDetails({super.key});

  @override
  State<BusinessKycDetails> createState() => _BusinessKycDetailsState();
}

class _BusinessKycDetailsState extends State<BusinessKycDetails> {
  final UserController userController = Get.find<UserController>();


  @override
  void initState(){
    super.initState();
              userController.getUserKycDetails();
  }
  @override
  Widget build(BuildContext context) {
    return 
    Scaffold(
        backgroundColor: AppColors.kwhite,
        appBar: AppBar(
          shadowColor: AppColors.kwhite,
          backgroundColor: AppColors.kwhite,
          surfaceTintColor: AppColors.kwhite,
          automaticallyImplyLeading: false,
          title: Row(
            children: [
              InkWell(
                hoverColor: Colors.transparent,
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  height: 32,
                  width: 32,
                  decoration: const BoxDecoration(
                      shape: BoxShape.circle, color: AppColors.scaffoldColor),
                  child: const Center(
                    child: Icon(Icons.close),
                  ),
                ),
              ),
              const Spacer(),
              Text("KYC details", style: titleTextSemiBold),
              const Spacer(),
            ],
          ),
        ),
        body: Obx(
          () => userController.isKycDetailsLoading.value ? const Center(child:  CircularProgressIndicator()) :
           Column(
            children: [
              const Divider(
                color: AppColors.backcolor,
                thickness: 1.0,
              ),
              Expanded(
                child: ScrollConfiguration(
                  behavior: ScrollConfiguration.of(context)
                      .copyWith(scrollbars: false),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 12),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Business Name",
                          style: bodyTextRegular.copyWith(
                              color: AppColors.txtsecondary),
                        ),
                        const Gap(4),
                        Text(
                          userController.kycDetailsModel.value.businessName,
                          style: bodyTextSemiBold,
                        ),
                        const Gap(25),
                        Text(
                          "Address",
                          style: bodyTextRegular,
                        ),
                        const Gap(4),
                        Text(
                          userController.kycDetailsModel.value.businessAddress,
                          maxLines: 5,
                          overflow: TextOverflow.ellipsis,
                          style: bodyTextSemiBold,
                        ),
                        const Gap(25),
                        Text(
                          "Document Type",
                          style: bodyTextRegular,
                        ),
                        const Gap(4),
                        Text(
                          userController.kycDetailsModel.value.documentType,
                          style: bodyTextSemiBold,
                        ),
                        const Gap(25),
                        userController.kycDetailsModel.value.idType.isEmpty
                            ? const SizedBox.shrink()
                            : Column(mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Id Type",
                                    style: bodyTextRegular,
                                  ),
                                  const Gap(4),
                                  Text(
                                    userController.kycDetailsModel.value.idType,
                                    style: bodyTextSemiBold,
                                  ),
                                ],
                              ),

                        const Gap(25),
                        Text(
                          "Document Number",
                          style: bodyTextRegular,
                        ),
                        const Gap(4),
                        Text(
                          userController.kycDetailsModel.value.documentNumber,
                          style: bodyTextSemiBold,
                        ),
                        const Gap(25),
                        Text(
                          "Status:",
                          style: bodyTextRegular,
                        ),
                        const Gap(4),
                        Text(
                          userController
                              .kycDetailsModel.value.status.capitalizeFirst
                              .toString(),
                               style: bodyTextSemiBold.copyWith(
                                  color:userController
                              .kycDetailsModel.value.status ==
                                          "approved"
                                      ? AppColors.kgreen
                                      : userController
                              .kycDetailsModel.value.status ==
                                              "pending"
                                          ? AppColors.kwarningbold
                                          : userController
                              .kycDetailsModel.value.status ==
                                                  "rejected"
                                              ? AppColors.errorRed
                                              : AppColors.txtprimary),
                        ),
                        const Gap(16),
                        userController.kycDetailsModel.value.reason.isEmpty
                            ? const SizedBox.shrink()
                            : Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Reason:",
                                    style: body2TextRegular.copyWith(
                                      decoration: TextDecoration.none,
                                    ),
                                  ),
                                  const Gap(4),
                                  Text(
                                    userController.kycDetailsModel.value.reason,
                                    style: body2TextSemiBold.copyWith(
                                      decoration: TextDecoration.none,
                                    ),
                                  ),
                                ],
                              ),
                        const Gap(20),
                        // Gap(16),
                        // Row(
                        //   children: [
                        //     Text(
                        //       "Done By:",
                        //       style: body2TextRegular.copyWith(
                        //         decoration: TextDecoration.none,
                        //       ),
                        //     ),
                        //     const Gap(20),
                        //     Text(
                        //       userController.kycDetailsModel.value.doneBy,
                        //       style: body2TextSemiBold.copyWith(
                        //         decoration: TextDecoration.none,
                        //       ),
                        //     ),
                        //   ],
                        // ),
                        //const Gap(20),
                        Text(
                          "Document",
                          style: body2TextRegular.copyWith(
                            decoration: TextDecoration.none,
                          ),
                        ),
                        const Gap(5),
                        Row(
                          children: [
                            SvgPicture.asset("assets/icons/Eye.svg"),
                            const Gap(5),
                            InkWell(
                              onTap: () {
                                _showDocumentDialog(
                                  userController
                                      .kycDetailsModel.value.documentUrl,
                                );
                                  // _launchURL( userController.kycDetailsModel.value.documentUrl);
                              },
                              child: Text(
                                "View document",
                                style: bodyTextSemiBold.copyWith(
                                    color: AppColors.kprimarycolor),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
        ));
  }

  void _showDocumentDialog(String documentUrl) async {
    final isPdf = documentUrl.endsWith('.pdf');

    if (kIsWeb && isPdf) {
      await _launchURL(documentUrl);
    } else {
      
      showDialog(
        context: Get.context!,
        barrierDismissible: true,
        builder: (context) {
          return Dialog(
            child: ImageViewPage(url: documentUrl),
          );
        },
      );
    }
  }

  Future<void> _launchURL(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await canLaunchUrl(url)) {
      throw Exception('Could not launch $url');
    }
    await launchUrl(url, mode: LaunchMode.externalApplication);
  }
}
