import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/view/landing_page/admin_loading_page.dart';
import 'package:parenthing_dashboard/view/landing_page/landing_page.dart';
import 'package:parenthing_dashboard/view/loginPage/loginpage.dart';

class WelcomePage extends StatefulWidget {
  const WelcomePage({super.key});

  @override
  State<WelcomePage> createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage> {
  @override
  void initState() {
    super.initState();
    checkUserLogin();
    super.initState();
  }

  void checkUserLogin() async {
    String isLogin = await storage.read("isLogin") ?? "";
    String rolID = await storage.read('roleID') ?? "";
    if (isLogin.isNotEmpty) {
      if (isLogin == "true") {
        if (rolID == "2") {
          Get.offAll(() => const AdminLoadingAnimationPage());
        } else {
          Get.offAll(() => const LoadingAnimationPage());
        }
      } else {
        Get.offAll(() => const LoginPage());
      }
    } else {
      Get.offAll(() => const LoginPage());
    }
  }

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator.adaptive(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.kprimarycolor),
      ),
    );
  }
}
