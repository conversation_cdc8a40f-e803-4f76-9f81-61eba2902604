import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'dart:async';

import 'package:parenthing_dashboard/view/loginPage/business_onboard_page.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final UserController userController = Get.find<UserController>();

  bool isOTP = false;
  void goBackToLogin() {
    setState(() {
      isOTP = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;
    bool isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom != 0;
    return Scaffold(
      backgroundColor: AppColors.backcolor,
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/BG.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            Center(
              child: Container(
                width: screenWidth <= 480
                    ? Get.width * 0.8 // Mobile (screen width up to 430)
                    : screenWidth <= 850
                        ? Get.width * 0.6 // Tablet (screen width up to 768)
                        : screenWidth <= 1080
                            ? Get.width *
                                0.4 // Laptop (screen width up to 1080)
                            : Get.width *
                                0.3, // Laptop/Desktop (screen width 1024 and above)
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.kwhite,
                  borderRadius: BorderRadius.circular(20.0),
                  border: Border.all(color: AppColors.kgrey, width: 1),
                ),
                child: SingleChildScrollView(
                  // padding: const EdgeInsets.only(bottom: 0.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Login to your account',
                        style: headingTextSemiBold.copyWith(
                            fontSize: screenWidth <= 480
                                ? 24
                                : screenWidth <= 850
                                    ? 26
                                    : screenWidth <= 1080
                                        ? 28
                                        : 32),
                        textAlign: TextAlign.center,
                      ),
                      const Gap(12),
                      Text(
                        'Welcome back! Please enter your registered phone number to continue.',
                        style: title3TextRegular.copyWith(
                            fontSize: screenWidth <= 480
                                ? 14
                                : screenWidth <= 850
                                    ? 18
                                    : screenWidth <= 1080
                                        ? 18
                                        : 18,
                            color: AppColors.txtsecondary),
                        textAlign: TextAlign.center,
                      ),
                      const Gap(30),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Text('Phone number', style: body2TextRegular),
                      ),
                      const Gap(10),
                      CustomTextFormField(
                        fontstyle: bodyTextMedium,
                        hintText: "Enter your phone number",
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        onChanged: (String value) {
                          userController.phoneController.value.text = value;
                        },
                        validator: (value) {
                          if (value!.isEmpty) {
                            return "Enter your phone";
                          }
                          return null;
                        },
                        maxLength: 10,
                        maxLines: 1,
                      ),
                      const Gap(30),
                      Obx(
                        () => PrimaryButton(
                          isLoading: userController.isSendOTPLoading.value,
                          text: "Request OTP",
                          onTap: () async {
                            if (userController
                                    .phoneController.value.text.length ==
                                10) {
                              userController.isSendOTPLoading.value = true;
                              await userController
                                  .sendOtpAPI(
                                      phone: userController
                                          .phoneController.value.text)
                                  .then((value) {
                                userController.isSendOTPLoading.value = false;
                                if (value == true) {
                                  setState(() {
                                    isOTP = true;
                                  });
                                } else {
                                  setState(() {
                                    isOTP = false;
                                  });
                                }
                              });
                            } else {
                              Get.snackbar("Error", "Enter valid mobile number",
                                  backgroundColor: Colors.red,
                                  maxWidth: 500.0,
                                  margin: const EdgeInsets.symmetric(
                                      vertical: 10.0, horizontal: 10.0),
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 10.0, horizontal: 10.0),
                                  colorText: Colors.white);
                            }
                          },
                        ),
                      ),
                      const Gap(20),
                      TextButton(
                          onPressed: () {
                            Get.to(() => const BusinessOnBoardingPage());
                          },
                          child: Text(
                            "New user? create a business account",
                            style: body2TextMedium,
                          ))
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              top: height * 0.1,
              left: 0,
              right: 0,
              child: Center(
                child: isKeyboardOpen
                    ? null
                    : Image.asset(
                        'assets/images/app_logo.png',
                        width: screenWidth <= 780
                            ? screenWidth * .5
                            : screenWidth * 0.3,
                      ),
              ),
            ),
            if (isOTP)
              Otp(
                (Function stopTimer) {
                  if (userController.otpController.value.text.length == 4) {
                    userController.isSendOTPLoading.value = true;
                    userController
                        .verifyOtpAPI(
                            otp: userController.otpController.value.text,
                            phone: userController.phoneController.value.text)
                        .then((value) {
                      if (value == true) {
                        stopTimer();
                        userController.isSendOTPLoading.value = false;
                        Get.snackbar("Success", "User login successfully.",
                            backgroundColor: AppColors.kprimarycolor,
                            maxWidth: 300.0,
                            snackStyle: SnackStyle.FLOATING,
                            colorText: AppColors.kwhite,
                            snackPosition: SnackPosition.TOP);
                      } else {
                        Get.snackbar(
                          "Failed",
                          "Enter valid OTP",
                          snackStyle: SnackStyle.FLOATING,
                          backgroundColor: Colors.red,
                          maxWidth: 200.0,
                          colorText: AppColors.kwhite,
                        );
                      }
                    });
                  } else {
                    Get.snackbar(
                        "Error", "Field cannot be blank. Please enter the OTP.",
                        backgroundColor: Colors.red,
                        maxWidth: 500.0,
                        margin: const EdgeInsets.symmetric(
                            vertical: 10.0, horizontal: 10.0),
                        padding: const EdgeInsets.symmetric(
                            vertical: 10.0, horizontal: 10.0),
                        colorText: Colors.white);
                  }
                },
                height,
                screenWidth,
                goBackToLogin,
              ),
          ],
        ),
      ),
    );
  }
}

class Otp extends StatefulWidget {
  final Function(Function stopTimer) onTap;

  final VoidCallback onBackPress;

  final double height;
  final double width;

  const Otp(this.onTap, this.height, this.width, this.onBackPress, {super.key});

  @override
  State<Otp> createState() => _OtpState();
}

class _OtpState extends State<Otp> {
  late Timer timer;
  int resendTimer = 45;
  bool isVerifying = false;
  final UserController userController = Get.find<UserController>();

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    timer = Timer.periodic(oneSec, (Timer t) {
      if (resendTimer == 0) {
        setState(() {
          timer.cancel();
        });
      } else {
        setState(() {
          resendTimer--;
        });
      }
    });
  }

  void stopTimer() {
    timer.cancel();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Align(
      alignment: Alignment.center,
      child: Container(
        width: screenWidth <= 480
            ? Get.width * 0.8 // Mobile (screen width up to 430)
            : screenWidth <= 850
                ? Get.width * 0.6 // Tablet (screen width up to 768)
                : screenWidth <= 1080
                    ? Get.width * 0.4 // Laptop (screen width up to 1080)
                    : Get.width *
                        0.3, // Laptop/Desktop (screen width 1024 and above)
        padding: const EdgeInsets.all(30.0),
        decoration: BoxDecoration(
          color: AppColors.kwhite,
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.only(bottom: 0.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: widget.onBackPress,
                  ),
                  Expanded(
                    child: Center(
                      child: Text(
                        'OTP verification',
                        style: rubikStyle.copyWith(
                          fontSize: screenWidth <= 480
                              ? 18
                              : screenWidth <= 850
                                  ? 22
                                  : screenWidth <= 1080
                                      ? 22
                                      : 25.0,
                          color: AppColors.kblack,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                  ),
                  const Gap(20),
                ],
              ),
              kSmHeight,
              Text(
                'Please enter the verification code we sent to your phone number',
                style: rubikStyle.copyWith(
                  fontSize: 16.0,
                  color: AppColors.secondary,
                  fontWeight: FontWeight.w300,
                ),
                textAlign: TextAlign.center,
              ),
              kHeight,
              CustomPinInput(
                length: 4,
                onChanged: (value) {
                  userController.otpController.value.text = value;
                },
              ),
          
              const Gap(30),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text("Didn't get the OTP? ",
                      style: body2TextRegular.copyWith(
                          color: AppColors.txtsecondary)),
                  resendTimer == 0
                      ? InkWell(
                          onTap: () {
                            if (resendTimer == 0) {
                              setState(() {
                                resendTimer = 45;
                                userController.sendOtpAPI(
                                    phone: userController
                                        .phoneController.value.text);
                              });
                              startTimer();
                            }
                          },
                          child: Center(
                            child: Text(
                              'Resend OTP',
                              style: body2TextMedium.copyWith(
                                color: resendTimer == 0
                                    ? AppColors.kprimarycolor
                                    : AppColors.ktertiary,
                              ),
                            ),
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Resend in ',
                              style: body2TextMedium,
                            ),
                            Text(
                              "00:${resendTimer < 10 ? '0$resendTimer' : resendTimer}",
                              style: body2TextMedium,
                            ),
                          ],
                        ),
                ],
              ),
              kMinHeight,
              Obx(
                () => PrimaryButton(
                  isLoading: userController.isSendOTPLoading.value,
                  text: "Login",
                  onTap: () => widget.onTap(stopTimer),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomPinInput extends StatefulWidget {
  final int length;
  final ValueChanged<String> onChanged;

  const CustomPinInput(
      {super.key, required this.length, required this.onChanged});

  @override
  // ignore: library_private_types_in_public_api
  _CustomPinInputState createState() => _CustomPinInputState();
}

class _CustomPinInputState extends State<CustomPinInput> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(widget.length, (_) => TextEditingController());
    _focusNodes = List.generate(widget.length, (_) => FocusNode());
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focus in _focusNodes) {
      focus.dispose();
    }
    super.dispose();
  }

  void _onChanged() {
    String pin = _controllers.map((c) => c.text).join();
    widget.onChanged(pin);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: List.generate(
        widget.length,
        (index) => SizedBox(
          width: 50,
          child: Center(
            child: TextField(
              controller: _controllers[index],
              focusNode: _focusNodes[index],
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(1),
              ],
              decoration: InputDecoration(
                hintText: '0',
                hintStyle:
                    body2TextMedium.copyWith(color: AppColors.placeHolder),
                fillColor: AppColors.backcolor,
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4.0),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4.0),
                  borderSide: BorderSide.none,
                ),
              ),
              style: bodyTextMedium,
              onChanged: (value) {
                if (value.isNotEmpty && index < widget.length - 1) {
                  _focusNodes[index + 1].requestFocus();
                }
                _onChanged();
              },
            ),
          ),
        ),
      ),
    );
  }
}
