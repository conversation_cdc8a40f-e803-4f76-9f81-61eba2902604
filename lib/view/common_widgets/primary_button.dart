import 'package:flutter/material.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class PrimaryButton extends StatelessWidget {
  final String text;
  final void Function() onTap;
  final double fontSize;
  final Color backgroundColor;
  final Color textColor;
  final EdgeInsets padding;
  final bool isLoading;

  const PrimaryButton({
    super.key,
    required this.text,
    required this.onTap,
    this.fontSize = 14.0,
    this.backgroundColor = AppColors.bluecolor,
    this.textColor = AppColors.kwhite,
    this.padding = const EdgeInsets.all(12.0),
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: isLoading ? null : onTap,
      child: Container(
        height: 48,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: isLoading
            ? const Center(
                child: SizedBox(
                  height: 38,
                  child: LoadingIndicator(
                    indicatorType: Indicator.ballPulse,
                    colors: [AppColors.kwhite],
                    strokeWidth: 4,
                  ),
                ),
              )
            : Center(
                child: Text(
                  text,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: body2TextBold.copyWith(color: textColor,fontSize: fontSize),
                ),
              ),
      ),
    );
  }
}

class CustomOutlinedButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final Color borderColor;
  final double borderWidth;
  final BorderRadius borderRadius;
  final TextStyle textStyle;

  const CustomOutlinedButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.borderColor = AppColors.ktertiary,
    this.borderWidth = 1.0,
    this.borderRadius = const BorderRadius.all(Radius.circular(5.0)),
    this.textStyle = const TextStyle(fontSize: 16.0),
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 42.0,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(width: 0.9, color: borderColor),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
          ),
        ),
        child: Text(
          text,
          style: textStyle,
        ),
      ),
    );
  }
}
