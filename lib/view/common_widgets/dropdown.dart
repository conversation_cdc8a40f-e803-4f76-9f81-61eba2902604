import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class CustomDropdownFormField<T> extends StatelessWidget {
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final FormFieldValidator<T>? validator;
  final T? value;
  final String hintText;
  final TextStyle? textStyle;

  const CustomDropdownFormField({
    super.key,
    required this.items,
    required this.hintText,
    this.onChanged,
    this.validator,
    this.value,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        splashColor: Colors.transparent,
        shadowColor: Colors.transparent,
        focusColor: Colors.transparent,
      ),
      child: DropdownButtonFormField2<T>(
        isExpanded: true,
        value: value,
        decoration: InputDecoration(
          contentPadding:
              const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(color: AppColors.errorRed),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(color: AppColors.errorRed),
          ),
          errorStyle: const TextStyle(color: AppColors.errorRed),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: BorderSide.none,
          ),
          fillColor: AppColors.backcolor,
          filled: true,
          hintText: hintText,
          hintStyle: bodyTextMedium.copyWith(color: AppColors.placeHolder),
        ),
        hint: Text(
          hintText,
          style: bodyTextMedium.copyWith(color: AppColors.placeHolder),
        ),
        items: items,
        onChanged: onChanged,
        validator: validator,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        style: textStyle ?? bodyTextMedium,
        buttonStyleData: const ButtonStyleData(
          padding: EdgeInsets.only(right: 8),
        ),
        iconStyleData: const IconStyleData(
          icon: Icon(
            Icons.keyboard_arrow_down_outlined,
            color: AppColors.bottomlightgrey,
          ),
          iconSize: 26,
        ),
        dropdownStyleData: DropdownStyleData(
          maxHeight: 300,
          elevation: 4,
          offset: const Offset(0, -5),
          decoration: BoxDecoration(
            color: AppColors.scaffoldColor,
            borderRadius: BorderRadius.circular(8),
          ),
          scrollbarTheme: ScrollbarThemeData(
            radius: const Radius.circular(40),
            thickness: MaterialStateProperty.all(6),
            thumbColor: MaterialStateProperty.all(Colors.grey[300]),
          ),
        ),
        menuItemStyleData: const MenuItemStyleData(
          height: 48,
          padding: EdgeInsets.symmetric(horizontal: 16),
        ),
      ),
    );
  }
}

class CustomDropdownUI extends StatelessWidget {
  final List<DropdownMenuItem<Object>>? items;
  final String? value, title, hintText;
  final Function(Object?)? onChanged;
  final void Function(Object?)? onSaved;
  final bool isValidatorReq;
  final bool isTitleRequired;
  final bool isRemoveButton;
  final Function()? onTapRemoveButton;
  final bool isReadOnly;

  const CustomDropdownUI(
      {super.key,
      this.items,
      this.value,
      this.onChanged,
      this.onSaved,
      this.title,
      this.isValidatorReq = false,
      this.isTitleRequired = true,
      this.hintText,
      this.isRemoveButton = false,
      this.onTapRemoveButton,
      this.isReadOnly = false});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title == "" ? const SizedBox() : Text(title!, style: body2TextRegular),
        const SizedBox(height: 5),
        Container(
          margin: const EdgeInsets.only(top: 4),
          child: DropdownButtonFormField2<Object>(
            isExpanded: true,
            decoration: InputDecoration(
              enabledBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.transparent),
                borderRadius: BorderRadius.circular(6),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.transparent),
                borderRadius: BorderRadius.circular(6),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.red),
                borderRadius: BorderRadius.circular(6),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.red),
                borderRadius: BorderRadius.circular(6),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
              fillColor: AppColors.scaffoldColor,
              filled: true,
              errorStyle: const TextStyle(color: Colors.red, height: 0.5),
              border: InputBorder.none,
            ),
            hint: Text(
              hintText ?? "Select",
              style: bodyTextMedium.copyWith(color: AppColors.placeHolder),
            ),
            items: items,
            value: value,
            validator: (value) {
              if (isValidatorReq == true) {
                if (value == null) {
                  return "Required";
                }
                return null;
              }
              return null;
            },
            onChanged: isReadOnly ? null : onChanged,
            onSaved: onSaved,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            style: bodyTextMedium,
            iconStyleData: const IconStyleData(
              icon: Icon(
                Icons.arrow_drop_down,
                color: AppColors.kgrey,
              ),
              iconSize: 36,
            ),
            dropdownStyleData: DropdownStyleData(
              maxHeight: 250,
              width: null,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: AppColors.scaffoldColor,
              ),
              offset: const Offset(0, -5),
              scrollbarTheme: ScrollbarThemeData(
                radius: const Radius.circular(40),
                thickness: MaterialStateProperty.all(6),
                thumbColor: MaterialStateProperty.all(Colors.grey[300]),
              ),
            ),
            menuItemStyleData: const MenuItemStyleData(
              height: 40,
              padding: EdgeInsets.only(left: 14, right: 14),
            ),
            buttonStyleData: const ButtonStyleData(
              padding: EdgeInsets.only(right: 8),
            ),
          ),
        ),
      ],
    );
  }
}

class CustomDropDown extends StatefulWidget {
  final List<String> items;
  final String? initialValue;
  final String hinttext;
  final String? Function(String?)? validator;
  final ValueChanged<String?>? onChanged;
  final Function(String?)? onSaved;
  final void Function()? onTap;

  const CustomDropDown({
    super.key,
    required this.items,
    this.initialValue,
    this.onChanged,
    this.onSaved,
    this.validator,
    this.onTap,
    this.hinttext = '',
  });

  @override
  State<CustomDropDown> createState() => _CustomDropDownState();
}

class _CustomDropDownState extends State<CustomDropDown> {
  String? _value;

  @override
  void initState() {
    super.initState();
    _value = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        splashColor: Colors.transparent,
        shadowColor: Colors.transparent,
        focusColor: Colors.transparent,
      ),
      child: DropdownButtonFormField2<String>(
        isExpanded: true,
        style: bodyTextMedium,
        decoration: InputDecoration(
          contentPadding:
              const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(color: AppColors.errorRed),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(color: AppColors.errorRed),
          ),
          errorStyle: const TextStyle(color: AppColors.errorRed),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: BorderSide.none,
          ),
          fillColor: AppColors.backcolor,
          filled: true,
          hintText: widget.hinttext,
          hintStyle: body2TextMedium.copyWith(color: AppColors.placeHolder),
        ),
        hint: Text(
          widget.hinttext,
          style: body2TextMedium.copyWith(color: AppColors.placeHolder),
        ),
        items: widget.items
            .map((item) => DropdownMenuItem<String>(
                  onTap: widget.onTap,
                  value: item,
                  child: Text(
                    item,
                    style: bodyTextMedium,
                  ),
                ))
            .toList(),
        onChanged: (value) {
          setState(() {
            _value = value;
          });
          widget.onChanged?.call(value);
        },
        value: _value,
        validator: widget.validator,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        onSaved: widget.onSaved,
        buttonStyleData: const ButtonStyleData(
          padding: EdgeInsets.only(right: 8),
        ),
        iconStyleData: const IconStyleData(
          icon: Icon(
            Icons.keyboard_arrow_down_outlined,
            color: AppColors.bottomlightgrey,
          ),
          iconSize: 26,
        ),
        dropdownStyleData: DropdownStyleData(
          maxHeight: 300,
          elevation: 4,
          offset: const Offset(0, -5),
          decoration: BoxDecoration(
            color: AppColors.scaffoldColor,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          scrollbarTheme: ScrollbarThemeData(
            radius: const Radius.circular(40),
            thickness: MaterialStateProperty.all(6),
            thumbColor: MaterialStateProperty.all(Colors.grey[300]),
          ),
        ),
        menuItemStyleData: const MenuItemStyleData(
          height: 48,
          padding: EdgeInsets.symmetric(horizontal: 16),
        ),
      ),
    );
  }
}
