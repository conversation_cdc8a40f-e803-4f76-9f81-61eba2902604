import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/common_widgets/animation.dart';
import 'package:parenthing_dashboard/view/common_widgets/empty_case.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/event/widgets/event_cell.dart';
import 'package:parenthing_dashboard/view/event/widgets/option_container.dart';

class EventPage extends StatefulWidget {
    final String? initialTab;
  const EventPage({super.key, this.initialTab});

  @override
  State<EventPage> createState() => _EventPageState();
}

class _EventPageState extends State<EventPage> {
  RxBool isPublished = true.obs;
  RxBool isDraft = false.obs;
  RxBool isPast = false.obs;
  RxBool isContentSelected = true.obs;
final EventController eventController = Get.find<EventController>();

  @override
  void initState() {
    super.initState();
        _setInitialTab();
    _fetchEventsBasedOnState();
  }



  void _setInitialTab() {
    if (widget.initialTab != null) {
      switch (widget.initialTab) {
        case 'draft':
          isPublished.value = false;
          isDraft.value = true;
          isPast.value = false;
          break;
        case 'past':
          isPublished.value = false;
          isDraft.value = false;
          isPast.value = true;
          break;
        case 'published':
        default:
          isPublished.value = true;
          isDraft.value = false;
          isPast.value = false;
          break;
      }
    }
  }


  void _fetchEventsBasedOnState() {
    Future.microtask(() {
      eventController.eventList.clear();
      if (isPublished.value) {
        eventController.getAllEventList("published");
      } else if (isDraft.value) {
        eventController.getAllEventList("draft");
      } else if (isPast.value) {
        eventController.getAllEventList("past");
      }
    });
  }


  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
           
              Container(
                width: Get.width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("My Events", style: heading2TextRegular),
                        PrimaryButton(
                          text: '+ Create event',
                          onTap: () {
                            log("+ Create event");
                            locator<NavigationServices>()
                                .navigateTo(
                              createEventPage,
                            ).then((value) {
                                if (value != null && value is String) {
                                setState(() {
                                  switch (value) {
                                    case 'draft':
                                      isPublished.value = false;
                                      isDraft.value = true;
                                      isPast.value = false;
                                      break;
                                    case 'published':
                                      isPublished.value = true;
                                      isDraft.value = false;
                                      isPast.value = false;
                                      break;
                                  }
                                });
                              }
                              _fetchEventsBasedOnState();
                            });
                          },
                        ),
                      ],
                    ),
                    Divider(
                      thickness: 1.0,
                      color: AppColors.ktertiary
                          .withOpacity(.3),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Container(
                        height: 46,
                        width: screenWidth >= 1025
                            ? screenWidth * .25
                            : screenWidth,
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.symmetric(
                            vertical: 4, horizontal: 4),
                        decoration: BoxDecoration(
                            color: AppColors.scaffoldColor,
                            borderRadius: BorderRadius.circular(50)),
                        child: Row(
                          children: [
                            Obx(
                              () => OptionContainer(
                                  text: 'Published',
                                  isSelected: isPublished.value,
                                  onTap: () {
                                    // eventController.getAllEventList("published");
                                    setState(() {
                                      isPublished.value = true;
                                      isDraft.value = false;
                                      isPast.value = false;
                                    });
                                    _fetchEventsBasedOnState();
                                  }),
                            ),
                            Obx(
                              () => OptionContainer(
                                text: 'Draft',
                                isSelected: isDraft.value,
                                onTap: () {
                                  // eventController.getAllEventList("draft");
                                  setState(() {
                                    isPublished.value = false;
                                    isDraft.value = true;
                                    isPast.value = false;
                                  });
                                  _fetchEventsBasedOnState();
                                },
                              ),
                            ),
                            Obx(
                              () => OptionContainer(
                                text: 'Past',
                                isSelected: isPast.value,
                                onTap: () {
                                  // eventController.getAllEventList("past");
                                  setState(
                                    () {
                                      isPublished.value = false;
                                      isDraft.value = false;
                                      isPast.value = true;
                                    },
                                  );
                                  _fetchEventsBasedOnState();
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    isPublished.value
                        ? Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 10),
                            child: publishedWidget(),
                          )
                        : isDraft.value
                            ? draftWidget()
                            : pastWidget(),
                    const Gap(20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  publishedWidget() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Obx(
      () => eventController.isEventListLoading.value
          ? const Center(
              child: CircularProgressIndicator.adaptive(),
            )
          : eventController.eventList.isEmpty
              ? SizedBox(
                  width: Get.width,
                  child: const EmptyCaseContainer(
                    type: "event_published",
                  ),
                )
              : GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: screenWidth <= 480
                        ? 1
                        : screenWidth <= 1024
                            ? 2
                            : 3,
                    mainAxisExtent: 133,
                    crossAxisSpacing: 10.0, // Adjusts spacing between columns
                    mainAxisSpacing: 10.0, // Adjusts spacing between rows
                  ),
                  itemCount: eventController.eventList.length,
                  itemBuilder: (context, index) {
                    return GridAnimationWidget(
                      columnCount: screenWidth <= 480
                          ? 1
                          : screenWidth <= 1024
                              ? 2
                              : 3,
                      index: index,
                      child: MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: EventCell(
                          currentTab: "published",
                          eventModelObject: eventController.eventList[index],
                          eventController: eventController,
                        ),
                      ),
                    );
                  },
                ),
    );
  }

  draftWidget() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Obx(
      () => eventController.isEventListLoading.value
          ? const Center(
              child: CircularProgressIndicator.adaptive(),
            )
          : eventController.eventList.isEmpty
              ? SizedBox(
                  width: Get.width,
                  child: const EmptyCaseContainer(
                    type: "event_published",
                  ),
                )
              : GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: screenWidth <= 480
                        ? 1
                        : screenWidth <= 1024
                            ? 2
                            : 3,
                    mainAxisExtent: 133,
                    crossAxisSpacing: 10.0, // Adjusts spacing between columns
                    mainAxisSpacing: 10.0, // Adjusts spacing between rows
                  ),
                  itemCount: eventController.eventList.length,
                  itemBuilder: (context, index) {
                    return GridAnimationWidget(
                      columnCount: screenWidth <= 480
                          ? 1
                          : screenWidth <= 1024
                              ? 2
                              : 3,
                      index: index,
                      child: MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: EventCell(
                          currentTab: "draft",
                          eventModelObject: eventController.eventList[index],
                          eventController: eventController,
                        ),
                      ),
                    );
                  },
                ),
    );
  }

  pastWidget() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Obx(
      () => eventController.isEventListLoading.value
          ? const Center(
              child: CircularProgressIndicator.adaptive(),
            )
          : eventController.eventList.isEmpty
              ? SizedBox(
                  width: Get.width,
                  child: const EmptyCaseContainer(
                    type: "event_published",
                  ),
                )
              : GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: screenWidth <= 480
                        ? 1
                        : screenWidth <= 1024
                            ? 2
                            : 3,

                    mainAxisExtent: 133,
                    crossAxisSpacing: 10.0, // Adjusts spacing between columns
                    mainAxisSpacing: 10.0, // Adjusts spacing between rows
                  ),
                  itemCount: eventController.eventList.length,
                  itemBuilder: (context, index) {
                    return GridAnimationWidget(
                      columnCount: screenWidth <= 480
                          ? 1
                          : screenWidth <= 1024
                              ? 2
                              : 3,
                      index: index,
                      child: MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: EventCell(
                          currentTab: "past",
                          eventModelObject: eventController.eventList[index],
                          eventController: eventController,
                        ),
                      ),
                    );
                  },
                ),
    );
  }
}
