import 'dart:developer';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/model/event/event_model.dart';
import 'package:parenthing_dashboard/model/location_model.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/common_widgets/confirm_popup.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'package:parenthing_dashboard/view/event/widgets/date_time_widget.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:parenthing_dashboard/view/profile/add_address.dart';

class CreateEventPage extends StatefulWidget {
  const CreateEventPage({super.key});

  @override
  State<CreateEventPage> createState() => _CreateEventPageState();
}

class _CreateEventPageState extends State<CreateEventPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
final BusinessController businessController = Get.find<BusinessController>();
  String eventType = '';
  RxString ticketType = ''.obs;
  RxBool isTicketPaid = true.obs;
  RxBool isTicketFree = false.obs;
  RxString multipleBooking = ''.obs;
  RxBool isMultipleTicketYes = true.obs;
  RxBool isMultipleTicketNo = false.obs;
  RxBool isWebUrl = false.obs;
  RxBool isWhatsAppNumber = true.obs;
  RxBool is1Selected = false.obs;
  RxBool is2Selected = false.obs;
  RxBool is3Selected = false.obs;
  String? _selectedMinAge;
  String? _selectedMaxAge;
  RxBool isOfflineSelected = false.obs;
  RxBool isOfflineOnlineSelected = false.obs;
  RxBool isOnlineSelected = true.obs;
  String selectedAddress = '';

  final List<String> _ageOptions =
      List<String>.generate(17, (index) => (index).toString());

  final List<String> _maxAgeOptions =
      List<String>.generate(17, (index) => (index + 2).toString())..add("18+");
 final EventController eventController = Get.find<EventController>();
  var createEventModel = EventModel(locationDetails: LocationDetails());
  String _imageUrl = '';
  final TextEditingController _descriptionController = TextEditingController();
  final int _maxLength = 500;
  final TextEditingController _titleController = TextEditingController();
  final int _maxTitleLength = 50;

  final TextEditingController _bookingController = TextEditingController();

  @override
  void initState() {
    super.initState();

    if (isOfflineSelected.value) {
      createEventModel.eventType = "offline";
    }
    if (isTicketPaid.value) {
      createEventModel.ticketType = "paid";
    }
    if (isMultipleTicketYes.value) {
      createEventModel.multiplePricing = "yes";
    }
    businessController.businessProfileDetails();
    _descriptionController.addListener(_updateDescCharaCount);
    _titleController.addListener(_updatetitleCharaCount);
    _bookingController.addListener(_updateBookingValue);
  }

  @override
  void dispose() {
    _descriptionController.removeListener(_updateDescCharaCount);
    _titleController.removeListener(_updatetitleCharaCount);
    _descriptionController.dispose();
    _titleController.dispose();
    _bookingController.removeListener(_updateBookingValue);
    _bookingController.dispose();
    super.dispose();
  }

  void _updateBookingValue() {
    if (isWebUrl.value) {
      createEventModel.ctaUrl = _bookingController.text;
    } else {
      createEventModel.ctaMobile = _bookingController.text;
    }
  }

  void _updateDescCharaCount() {
    setState(() {});
  }

  void _updatetitleCharaCount() {
    setState(() {});
  }

  void _updateDuration() {
    DateTime startDateTime = getValidDateTime(createEventModel.startDate,
        defaultValue: DateTime.now());
    DateTime endDateTime = getValidDateTime(createEventModel.endDate,
        defaultValue: startDateTime.add(const Duration(minutes: 30)));

    Duration difference = endDateTime.difference(startDateTime);

    createEventModel.duration = difference.inMinutes;
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
        backgroundColor: AppColors.kwhite,
        body: ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Container(
              width: screenWidth <= 820 ? Get.width : Get.width * .7,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(
                  color: AppColors.kgrey,
                ),
              ),
              child: Form(
                key: _formKey,
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(15),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            InkWell(
                              onTap: () {
                                showDialog(
                                  context: context,
                                  barrierDismissible: false,
                                  builder: (BuildContext context) {
                                    return CustomDialog(
                                      onConfirmTxt: "Yes, leave",
                                      onCancelText: "No",
                                      title: 'Leave this page?',
                                      content:
                                          'Are you sure you want to leave this page? All field details will be discarded',
                                      image: "assets/icons/WarningCircle.svg",
                                      onConfirm: () {
                                        Navigator.of(context).pop();
                                        locator<NavigationServices>().goBack();
                                      },
                                      onCancel: () {
                                        Navigator.of(context).pop();
                                      },
                                    );
                                  },
                                );
                              },
                              child: SvgPicture.asset(
                                  'assets/icons/arrow-left.svg',
                                  fit: BoxFit.fill,
                                  height: 32,
                                  width: 32),
                            ),
                            mdWidth,
                            Text("Create an Event", style: heading2TextRegular),
                          ],
                        ),
                      ),
                      const Divider(
                        thickness: 1.0,
                        color: AppColors.kgrey,
                      ),
                      screenWidth <= 820
                          ? Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildEventBannerField(),
                                  _buildEventNameField(),
                                ],
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  Expanded(child: _buildEventNameField()),
                                  const Gap(20),
                                  _buildEventBannerField(),
                                ],
                              ),
                            ),
                      const Divider(
                        thickness: 1.0,
                        color: AppColors.kgrey,
                      ),
                      screenWidth <= 820
                          ? Column(
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                        child: _buildStartDateTime(context)),
                                  ],
                                ),
                                Row(
                                  children: [
                                    Expanded(child: _buildEndDateTime(context)),
                                  ],
                                ),
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(child: _buildStartDateTime(context)),
                                const Gap(20),
                                Expanded(child: _buildEndDateTime(context)),
                              ],
                            ),
                      const Gap(20),
                      _buildDurationDisplay(),
                      // _buildDurationDropdown(),
                      const Gap(10),
                      const Divider(thickness: 1.0, color: AppColors.kgrey),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          screenWidth <= 820
                              ? Column(
                                  children: [
                                    buildAgeGroupSection(),
                                    buildEventTypeSection()
                                  ],
                                )
                              : Row(
                                  children: [
                                    Expanded(
                                      child: buildAgeGroupSection(),
                                    ),
                                    const Gap(20),
                                    Expanded(child: buildEventTypeSection())
                                  ],
                                ),
                          screenWidth < 820
                              ? Visibility(
                                  visible: !isOnlineSelected.value,
                                  child: Column(
                                    children: [
                                      //_buildEventCity(),
                                      buildEventAddress()
                                    ],
                                  ))
                              : Visibility(
                                  visible: !isOnlineSelected.value,
                                  child: Row(
                                    children: [
                                      // Expanded(
                                      //   child: _buildEventCity(),
                                      // ),
                                      Expanded(child: buildEventAddress()),
                                    ],
                                  ),
                                ),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          screenWidth <= 820
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    buildTicketTypeSection(),
                                    if (isTicketFree.value)
                                      SizedBox(
                                          width: Get.width,
                                          child:
                                              buildMultipleTicketPricingSection()),
                                  ],
                                )
                              : Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(child: buildTicketTypeSection()),
                                    const SizedBox(width: 30),
                                    isTicketFree.value
                                        ? const Spacer()
                                        : Expanded(
                                            child:
                                                buildMultipleTicketPricingSection())
                                  ],
                                ),
                          isTicketFree.value
                              ? const SizedBox()
                              : Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Price starts at*",
                                          style: body2TextRegular),
                                      const Gap(10),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child: CustomTextFormField(
                                              initialValue: "",
                                              onChanged: (val) {
                                                int price =
                                                    int.tryParse(val) ?? 0;
                                                createEventModel.price = price;
                                              },
                                              hintText: "Enter amount",
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Amount Required';
                                                }
                                                return null;
                                              },
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .digitsOnly
                                              ],
                                              maxLength: 8,
                                              prefixIcon: const Padding(
                                                padding:
                                                    EdgeInsets.only(right: 8),
                                                child: Icon(
                                                    Icons.currency_rupee,
                                                    size: 20,
                                                    color: AppColors
                                                        .bottomlightgrey),
                                              ),
                                            ),
                                          ),
                                          const Spacer()
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                          const Gap(10),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Bookings", style: title3TextSemiBold),
                                const Gap(15),
                                screenWidth <= 820
                                    ? Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text("Preferred ticket booking*",
                                              style: body2TextRegular),
                                          const Gap(8),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: InkWell(
                                                  onTap: () {
                                                    setState(() {
                                                      isWebUrl.value = true;
                                                      isWhatsAppNumber.value =
                                                          false;
                                                      createEventModel
                                                          .ctaMobile = "";
                                                      _bookingController
                                                          .clear();
                                                    });
                                                  },
                                                  child: Container(
                                                    height: 42,
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          const BorderRadius
                                                              .horizontal(
                                                        left:
                                                            Radius.circular(12),
                                                      ),
                                                      color: isWebUrl.value
                                                          ? AppColors.kwhite
                                                          : AppColors
                                                              .scaffoldColor,
                                                      border: Border.all(
                                                        color: isWebUrl.value
                                                            ? AppColors
                                                                .txtprimary
                                                            : AppColors
                                                                .bordergrey,
                                                      ),
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        "Web URL",
                                                        style: body2TextMedium.copyWith(
                                                            fontWeight:
                                                                isWebUrl.value
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .w500,
                                                            color: isWebUrl
                                                                    .value
                                                                ? AppColors
                                                                    .txtprimary
                                                                : AppColors
                                                                    .txtsecondary),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                child: InkWell(
                                                  onTap: () {
                                                    setState(() {
                                                      isWebUrl.value = false;
                                                      isWhatsAppNumber.value =
                                                          true;
                                                      createEventModel.ctaUrl =
                                                          "";
                                                      _bookingController
                                                          .clear();
                                                    });
                                                  },
                                                  child: Container(
                                                    height: 42,
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          const BorderRadius
                                                              .horizontal(
                                                        right:
                                                            Radius.circular(12),
                                                      ),
                                                      color: isWhatsAppNumber
                                                              .value
                                                          ? AppColors.kwhite
                                                          : AppColors
                                                              .scaffoldColor,
                                                      border: Border.all(
                                                        color: isWhatsAppNumber
                                                                .value
                                                            ? AppColors
                                                                .txtprimary
                                                            : AppColors
                                                                .bordergrey,
                                                      ),
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        "WhatsApp number",
                                                        style: body2TextMedium.copyWith(
                                                            fontWeight:
                                                                isWhatsAppNumber
                                                                        .value
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .w500,
                                                            color: isWhatsAppNumber
                                                                    .value
                                                                ? AppColors
                                                                    .txtprimary
                                                                : AppColors
                                                                    .txtsecondary),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const Gap(15),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                  isWebUrl.value
                                                      ? "Booking URL*"
                                                      : "WhatsApp Number*",
                                                  style: body2TextRegular),
                                              const Gap(8),
                                              CustomTextFormField(
                                                controller: _bookingController,
                                                onChanged: (p0) {
                                                  if (isWebUrl.value) {
                                                    createEventModel.ctaUrl =
                                                        p0;
                                                  } else {
                                                    createEventModel.ctaMobile =
                                                        "+91$p0";
                                                  }
                                                },
                                                // onChanged: (val) {
                                                //   if (isWebUrl.value) {
                                                //     createEventModel.ctaUrl =
                                                //         val;
                                                //   } else {
                                                //     createEventModel.ctaMobile =
                                                //         val;
                                                //   }
                                                // },
                                                keyboardType: isWebUrl.value
                                                    ? TextInputType.url
                                                    : TextInputType.phone,
                                                inputFormatters: isWebUrl.value
                                                    ? []
                                                    : [
                                                        FilteringTextInputFormatter
                                                            .digitsOnly,
                                                        LengthLimitingTextInputFormatter(
                                                            10),
                                                      ],
                                                validator: (value) {
                                                  if (value == null ||
                                                      value.isEmpty) {
                                                    return isWebUrl.value
                                                        ? 'URL Required'
                                                        : 'WhatsApp Number Required';
                                                  }
                                                  if (isWebUrl.value) {
                                                    final urlPattern = RegExp(
                                                        r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$');
                                                    if (!urlPattern
                                                        .hasMatch(value)) {
                                                      return 'Please enter a valid URL (e.g., https://example.com)';
                                                    }
                                                  } else {
                                                    if (value.length != 10) {
                                                      return 'Mobile number must be 10 digits';
                                                    }
                                                    if (!RegExp(
                                                            r'^[6-9][0-9]{9}$')
                                                        .hasMatch(value)) {
                                                      return 'Please enter a valid Indian mobile number';
                                                    }
                                                  }

                                                  return null;
                                                },
                                                hintText: isWebUrl.value
                                                    ? "e.g https://www.google.com"
                                                    : "+91-9001122445",
                                                prefixIcon: isWebUrl.value
                                                    ? null
                                                    : Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                horizontal: 12,
                                                                vertical: 16),
                                                        child: Text(
                                                          "+91",
                                                          style: body2TextMedium
                                                              .copyWith(
                                                                  color: AppColors
                                                                      .txtprimary),
                                                        ),
                                                      ),
                                                maxLength:
                                                    isWebUrl.value ? null : 10,
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                    "Preferred ticket booking*",
                                                    style: body2TextRegular),
                                                const Gap(8),
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: InkWell(
                                                        onTap: () {
                                                          setState(() {
                                                            isWebUrl.value =
                                                                true;
                                                            isWhatsAppNumber
                                                                .value = false;
                                                            createEventModel
                                                                .ctaMobile = "";
                                                            _bookingController
                                                                .clear();
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 42,
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                const BorderRadius
                                                                    .horizontal(
                                                              left: Radius
                                                                  .circular(12),
                                                            ),
                                                            color: isWebUrl
                                                                    .value
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .scaffoldColor,
                                                            border: Border.all(
                                                              color: isWebUrl
                                                                      .value
                                                                  ? AppColors
                                                                      .txtprimary
                                                                  : AppColors
                                                                      .bordergrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "Web URL",
                                                              style: body2TextMedium.copyWith(
                                                                  fontWeight: isWebUrl
                                                                          .value
                                                                      ? FontWeight
                                                                          .w600
                                                                      : FontWeight
                                                                          .w500,
                                                                  color: isWebUrl
                                                                          .value
                                                                      ? AppColors
                                                                          .txtprimary
                                                                      : AppColors
                                                                          .txtsecondary),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: InkWell(
                                                        onTap: () {
                                                          setState(() {
                                                            isWebUrl.value =
                                                                false;
                                                            isWhatsAppNumber
                                                                .value = true;
                                                            createEventModel
                                                                .ctaUrl = "";
                                                            _bookingController
                                                                .clear();
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 42,
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                const BorderRadius
                                                                    .horizontal(
                                                              right: Radius
                                                                  .circular(12),
                                                            ),
                                                            color: isWhatsAppNumber
                                                                    .value
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .scaffoldColor,
                                                            border: Border.all(
                                                              color: isWhatsAppNumber
                                                                      .value
                                                                  ? AppColors
                                                                      .txtprimary
                                                                  : AppColors
                                                                      .bordergrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "WhatsApp number",
                                                              style: body2TextMedium.copyWith(
                                                                  fontWeight: isWhatsAppNumber
                                                                          .value
                                                                      ? FontWeight
                                                                          .w600
                                                                      : FontWeight
                                                                          .w500,
                                                                  color: isWhatsAppNumber
                                                                          .value
                                                                      ? AppColors
                                                                          .txtprimary
                                                                      : AppColors
                                                                          .txtsecondary),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          const Gap(30),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                    isWebUrl.value
                                                        ? "Booking URL*"
                                                        : "WhatsApp Number*",
                                                    style: body2TextRegular),
                                                const Gap(8),
                                                CustomTextFormField(
                                                  controller:
                                                      _bookingController,
                                                  onChanged: (p0) {
                                                    if (isWebUrl.value) {
                                                      createEventModel.ctaUrl =
                                                          p0;
                                                    } else {
                                                      createEventModel
                                                          .ctaMobile = "+91$p0";
                                                    }
                                                  },
                                                  keyboardType: isWebUrl.value
                                                      ? TextInputType.url
                                                      : TextInputType.phone,
                                                  inputFormatters:
                                                      isWebUrl.value
                                                          ? []
                                                          : [
                                                              FilteringTextInputFormatter
                                                                  .digitsOnly,
                                                              LengthLimitingTextInputFormatter(
                                                                  10),
                                                            ],
                                                  validator: (value) {
                                                    if (value == null ||
                                                        value.isEmpty) {
                                                      return isWebUrl.value
                                                          ? 'URL Required'
                                                          : 'WhatsApp Number Required';
                                                    }

                                                    if (isWebUrl.value) {
                                                      // URL validation
                                                      final urlPattern = RegExp(
                                                          r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$');
                                                      if (!urlPattern
                                                          .hasMatch(value)) {
                                                        return 'Please enter a valid URL (e.g., https://example.com)';
                                                      }
                                                    } else {
                                                      // Mobile number validation
                                                      if (value.length != 10) {
                                                        return 'Mobile number must be 10 digits';
                                                      }
                                                      if (!RegExp(
                                                              r'^[6-9][0-9]{9}$')
                                                          .hasMatch(value)) {
                                                        return 'Please enter a valid Indian mobile number';
                                                      }
                                                    }

                                                    return null;
                                                  },
                                                  hintText: isWebUrl.value
                                                      ? "e.g https://www.google.com"
                                                      : "9001122445",
                                                  prefixIcon: isWebUrl.value
                                                      ? null
                                                      : Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal:
                                                                      12,
                                                                  vertical: 16),
                                                          child: Text(
                                                            "+91",
                                                            style: body2TextMedium
                                                                .copyWith(
                                                                    color: AppColors
                                                                        .txtprimary),
                                                          ),
                                                        ),
                                                  maxLength: isWebUrl.value
                                                      ? null
                                                      : 10,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                              ],
                            ),
                          ),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          screenWidth <= 820
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.info_outline),
                                          const Gap(8),
                                          Expanded(
                                            child: Text(
                                              "Please fill out all required fields to submit for review",
                                              style: bodyTextRegular.copyWith(
                                                  color:
                                                      AppColors.txtsecondary),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Gap(15),
                                    buildSubmitButton(),
                                    const Gap(25),
                                  ],
                                )
                              : Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          const Icon(Icons.info_outline),
                                          const Gap(8),
                                          Text(
                                            "Please fill out all required fields to submit for review.",
                                            overflow: TextOverflow.ellipsis,
                                            style: bodyTextRegular.copyWith(
                                                color: AppColors.txtsecondary),
                                            maxLines: 2,
                                          ),
                                        ],
                                      ),
                                      buildSubmitButton(),
                                    ],
                                  ))
                        ],
                      ),
                    ]),
              ),
            ),
          ),
        ));
  }

  Widget _buildEventNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        const Gap(10),
        Text('Event name (max.50 char)*', style: body2TextRegular),
        const Gap(10),
        CustomTextFormField(
          hintText: 'e.g Art attack',
          maxLength: _maxTitleLength,
          controller: _titleController,
          maxLines: 20,
          onChanged: (val) {
            createEventModel.title = val;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Event Title Required';
            }
            return null;
          },
        ),
        const Gap(10),
        Align(
            alignment: Alignment.centerRight,
            child: Text(
                '${_titleController.text.length}/$_maxTitleLength characters',
                style: body3TextRegular)),
        const Gap(30),
        Text('Description (max.500 char)*', style: body2TextRegular),
        const Gap(10),
        CustomTextFormField(
          controller: _descriptionController,
          onChanged: (val) {
            createEventModel.description = val;
          },
          hintText: "Type here",
          maxLines: 60,
          maxLength: _maxLength,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Event Desc. Required';
            }
            return null;
          },
        ),
        const Gap(10),
        Align(
            alignment: Alignment.centerRight,
            child: Text(
                '${_descriptionController.text.length}/$_maxLength characters',
                style: body3TextRegular)),
      ],
    );
  }

  Widget _buildEventBannerField() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Event banner*", style: body2TextRegular),
        const Gap(10),
        _imageUrl.isEmpty
            ? DottedBorder(
                color: AppColors.bordergrey,
                strokeWidth: 1,
                dashPattern: const [6, 3],
                borderType: BorderType.RRect,
                radius: const Radius.circular(4),
                child: Container(
                  height: screenWidth <= 820 ? 200 : 260,
                  width: screenWidth <= 820 ? Get.width : null,
                  // margin: const EdgeInsets.only(top: 10),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.kwhite,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const Icon(Icons.add_photo_alternate_outlined,
                            size: 60, color: AppColors.kprimarycolor),
                        const Gap(10),
                        Text(
                          "JPEG or PNG file formats supported in\n1080 x 1080px dimension, up to 1 MB",
                          style: body2TextRegular.copyWith(
                              color: AppColors.txtsecondary),
                        ),
                        const Gap(15),
                        TextButton(
                          child: Text(
                            'Upload Image',
                            textAlign: TextAlign.center,
                            style: bodyTextBold.copyWith(
                              color: const Color(0xff5E57E1),
                            ),
                          ),
                          onPressed: () async {
                            log("Upload Image button pressed");
                            FilePickerResult? result =
                                await FilePicker.platform.pickFiles();

                            if (result != null) {
                              PlatformFile pickedFile = result.files.first;
                              Uint8List? fileBytes = result.files.first.bytes;
                              log("File selected: ${pickedFile.name}");
                              await _uploadFileHelper(
                                  pickedFile, null, fileBytes!);
                            } else {
                              log('No file selected.');
                            }
                          },
                        ),
                      ]),
                ),
              )
            : Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      height: 260,
                      width: screenWidth <= 820 ? Get.width : 260,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: NetworkImage(_imageUrl),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 5,
                    right: 4,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _imageUrl = '';
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: AppColors.kprimarycolor,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: const Icon(
                          Icons.edit,
                          size: 20,
                          color: AppColors.kwhite,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ],
    );
  }

  Widget _buildStartDateTime(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: DateTimeWidget(
        title1: "Start Date*",
        title2: "Start Time*",
        onTapDate: () {
          final DateTime now = DateTime.now();
          final DateTime minimumStartDate = now.add(const Duration(days: 1));
          showDatePicker(
            barrierDismissible: false,
            context: context,
            initialDate: minimumStartDate,
            firstDate: minimumStartDate,
            lastDate: DateTime(2101),
          ).then((selectedDate) {
            if (selectedDate != null) {
              setState(() {
                createEventModel.startDate = formatDateTime(selectedDate);
                // Check if the end date is now before the start date
                DateTime startDate = getValidDateTime(
                    createEventModel.startDate,
                    defaultValue: minimumStartDate);
                DateTime endDate = getValidDateTime(createEventModel.endDate,
                    defaultValue: startDate.add(const Duration(minutes: 30)));

                if (endDate.isBefore(startDate)) {
                  createEventModel.endDate = formatDateTime(
                      startDate.add(const Duration(minutes: 30)));
                }
              });
            }
          });
        },
        onTapTime: () {
          eventController.selectTimeFn(context, true).then((value) {
            setState(() {
              DateTime originalDate = getValidDateTime(
                  createEventModel.startDate,
                  defaultValue: DateTime.now());
              List<String> parts = eventController.startTime.value.split(':');
              int hours = int.tryParse(parts[0]) ?? 0;
              int minutes = int.tryParse(parts[1]) ?? 0;
              Duration timeToAdd = Duration(hours: hours, minutes: minutes);
              DateTime newDateTime = originalDate.add(timeToAdd);

              createEventModel.startDate = formatDateTime(newDateTime);
            });
          });
        },
        selectedDate: createEventModel.startDate,
        selectedTime: createEventModel.startDate,
      ),
    );
  }

  Widget _buildEndDateTime(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: DateTimeWidget(
        title1: "End Date*",
        title2: "End Time*",
        onTapDate: () {
          DateTime startDate = getValidDateTime(createEventModel.startDate,
              defaultValue: DateTime.now());
          DateTime endDate = getValidDateTime(createEventModel.endDate,
              defaultValue: startDate.add(const Duration(minutes: 30)));

          showDatePicker(
            context: context,
            initialDate: endDate.isAfter(startDate)
                ? endDate
                : startDate.add(const Duration(days: 1)),
            firstDate: startDate,
            lastDate: DateTime(2101),
            barrierDismissible: false,
          ).then((selectedDate) {
            if (selectedDate != null) {
              setState(() {
                DateTime currentEndDateTime = getValidDateTime(
                    createEventModel.endDate,
                    defaultValue: endDate);
                DateTime newEndDateTime = DateTime(
                  selectedDate.year,
                  selectedDate.month,
                  selectedDate.day,
                  currentEndDateTime.hour,
                  currentEndDateTime.minute,
                );
                createEventModel.endDate = formatDateTime(newEndDateTime);
              });
            }
          });
        },
        onTapTime: () {
          DateTime startDateTime = getValidDateTime(createEventModel.startDate,
              defaultValue: DateTime.now());
          DateTime currentEndDateTime = getValidDateTime(
              createEventModel.endDate,
              defaultValue: startDateTime.add(const Duration(minutes: 30)));

          DateTime minEndDateTime =
              startDateTime.add(const Duration(minutes: 30));

          TimeOfDay initialTime = currentEndDateTime.isAfter(minEndDateTime)
              ? TimeOfDay.fromDateTime(currentEndDateTime)
              : TimeOfDay.fromDateTime(minEndDateTime);

          showTimePicker(
            context: context,
            initialTime: initialTime,
            barrierDismissible: false,
            builder: (BuildContext context, Widget? child) {
              return MediaQuery(
                data: MediaQuery.of(context)
                    .copyWith(alwaysUse24HourFormat: false),
                child: child!,
              );
            },
          ).then((TimeOfDay? timeOfDay) {
            if (timeOfDay != null) {
              setState(() {
                DateTime selectedEndDateTime = DateTime(
                  currentEndDateTime.year,
                  currentEndDateTime.month,
                  currentEndDateTime.day,
                  timeOfDay.hour,
                  timeOfDay.minute,
                );
                if (selectedEndDateTime.isBefore(minEndDateTime)) {
                  selectedEndDateTime = minEndDateTime;
                }
                createEventModel.endDate = formatDateTime(selectedEndDateTime);
              });
            }
          });
        },
        selectedDate: createEventModel.endDate,
        selectedTime: createEventModel.endDate,
      ),
    );
  }

  Widget _buildDurationDisplay() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Duration", style: body2TextRegular),
          const Gap(10),
          GetBuilder<EventController>(
            builder: (controller) {
              _updateDuration();
              String durationText =
                  _formatDuration(Duration(minutes: createEventModel.duration));

              return Text(
                durationText,
                style: body2TextRegular.copyWith(fontWeight: FontWeight.bold),
              );
            },
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    int days = duration.inDays;
    int hours = duration.inHours % 24;
    int minutes = duration.inMinutes % 60;

    List<String> parts = [];

    if (days > 0) {
      parts.add('$days ${days == 1 ? 'day' : 'days'}');
    }
    if (hours > 0 || days > 0) {
      // Always show hours if there are days
      parts.add('$hours ${hours == 1 ? 'hour' : 'hours'}');
    }
    if (minutes > 0 || hours > 0 || days > 0) {
      // Always show minutes
      parts.add('$minutes ${minutes == 1 ? 'minute' : 'minutes'}');
    }

    if (parts.isEmpty) {
      return '0 minutes';
    }

    return parts.join(' ');
  }

  // Widget _buildDurationDropdown() {
  //   double screenWidth = MediaQuery.of(context).size.width;
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 16),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text("Duration (in mins.)*", style: body2TextRegular),
  //         const Gap(10),
  //         SizedBox(
  //           width: screenWidth <= 820 ? Get.width / 2.5 : Get.width / 6.2,
  //           child: CustomDropDown(
  //             items: eventController.duration.map((e) => e.toString()).toList(),
  //             hinttext: "select",
  //            initialValue: null,
  //             onChanged: (value) {
  //               eventController.selectedDuration = int.parse(value!);
  //               createEventModel.duration = int.parse(value);
  //             },
  //             onSaved: (p0) {},
  //             onTap: () {},
  //             validator: (value) {
  //               if (value == null || value.isEmpty) {
  //                 return 'Duration Required';
  //               }
  //               return null;
  //             },
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget buildAgeGroupSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("For age group*", style: body2TextRegular),
          const Gap(8),
          Row(
            children: [
              Expanded(
                child: CustomDropdownFormField(
                  items: _ageOptions.map((age) {
                    return DropdownMenuItem(
                      value: age,
                      child: Text(age, style: bodyTextMedium),
                    );
                  }).toList(),
                  value: _selectedMinAge,
                  hintText: 'Min.age',
                  onChanged: (value) {
                    setState(() {
                      _selectedMinAge = value;
                      createEventModel.minAge = int.parse(value!);
                      log("Selected min age: $value");
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Min Age Required';
                    }
                    return null;
                  },
                ),
              ),
              const Gap(10),
              Text(
                'To',
                style: body3TextRegular.copyWith(color: AppColors.txtsecondary),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: CustomDropdownFormField(
                  items: _maxAgeOptions.map((age) {
                    return DropdownMenuItem(
                      value: age,
                      child: Text(age, style: bodyTextMedium),
                    );
                  }).toList(),
                  value: _selectedMaxAge,
                  hintText: 'Max.age',
                  onChanged: (value) {
                    setState(() {
                      _selectedMaxAge = value;

                      if (_selectedMaxAge == "18+") {
                        createEventModel.maxAge = 19;
                      } else {
                        try {
                          int selectedMaxAge = int.parse(value!);
                          int selectedMinAge = _selectedMinAge == "18+"
                              ? 18
                              : int.parse(_selectedMinAge!);

                          if (selectedMaxAge <= selectedMinAge) {
                            Get.snackbar(
                              "Error",
                              "The maximum age should be greater than the minimum age.",
                              snackStyle: SnackStyle.FLOATING,
                              backgroundColor: AppColors.errorRed,
                              maxWidth: 300,
                              colorText: AppColors.kwhite,
                            );
                          } else {
                            createEventModel.maxAge = selectedMaxAge;
                          }
                        } catch (e) {
                          Get.snackbar(
                            "Error",
                            "Invalid age value.",
                            snackStyle: SnackStyle.FLOATING,
                            backgroundColor: AppColors.errorRed,
                            maxWidth: 300,
                            colorText: AppColors.kwhite,
                          );
                        }
                      }
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Max Age Required';
                    } else if (_selectedMinAge == "18+" && value == "18+") {
                      return null;
                    } else if (value == "18+") {
                      return null;
                    } else {
                      try {
                        int selectedMaxAge = int.parse(value);
                        int selectedMinAge = _selectedMinAge == "18+"
                            ? 18
                            : int.parse(_selectedMinAge!);

                        if (selectedMaxAge <= selectedMinAge) {
                          return "The maximum age should be greater than the minimum age.";
                        }
                      } catch (e) {
                        return "Invalid age value.";
                      }
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildEventTypeSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Event type*", style: body2TextRegular),
          const Gap(8),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isOfflineSelected.value = true;
                      isOnlineSelected.value = false;
                      isOfflineOnlineSelected.value = false;
                      createEventModel.eventType = "offline";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(8),
                        right: Radius.zero,
                      ),
                      color: isOfflineSelected.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isOfflineSelected.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Offline",
                        style: body2TextMedium.copyWith(
                            fontWeight: isOfflineOnlineSelected.value
                                ? FontWeight.w600
                                : FontWeight.w500,
                            color: isOfflineOnlineSelected.value
                                ? AppColors.txtprimary
                                : AppColors.txtsecondary),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isOfflineSelected.value = false;
                      isOnlineSelected.value = true;
                      isOfflineOnlineSelected.value = false;
                      createEventModel.eventType = "online";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                        color: isOnlineSelected.value
                            ? AppColors.kwhite
                            : AppColors.scaffoldColor,
                        border: Border.all(
                          color: isOnlineSelected.value
                              ? AppColors.txtprimary
                              : AppColors.bordergrey,
                        )),
                    child: Center(
                      child: Text("Online",
                          style: body2TextMedium.copyWith(
                              fontWeight: isOnlineSelected.value
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isOnlineSelected.value
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      isOfflineSelected.value = false;
                      isOnlineSelected.value = false;
                      isOfflineOnlineSelected.value = true;
                      createEventModel.eventType = "offline + online";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(8),
                      ),
                      color: isOfflineOnlineSelected.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                          color: isOfflineOnlineSelected.value
                              ? AppColors.txtprimary
                              : AppColors.bordergrey),
                    ),
                    child: Center(
                      child: Text("Offline + Online",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: body2TextMedium.copyWith(
                              fontWeight: isOfflineOnlineSelected.value
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isOfflineOnlineSelected.value
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Widget _buildEventCity() {
  //   return Padding(
  //     padding: const EdgeInsets.all(16),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [

  //         Text("City*", style: body2TextRegular),
  //         const Gap(10),
  //         CustomDropDown(
  //           items: eventController.serviceableCity,
  //           hinttext: "Select",
  //           onChanged: (value) {
  //             createEventModel.city = value!;
  //           },
  //           initialValue: null,
  //           onSaved: (p0) {},
  //           onTap: () {},
  //           validator: (value) {
  //             if (value == null || value.isEmpty) {
  //               return 'City Required';
  //             }
  //             return null;
  //           },
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget buildEventAddress() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomDropdownUI(
            isValidatorReq: true,
            title: "Address",
            items: [
              ...() {
                final uniqueAddresses = <String>{};
                return businessController.userModel.value.location
                    .where((location) =>
                        location.address.isNotEmpty &&
                        uniqueAddresses.add(location.address))
                    .map((location) {
                  return DropdownMenuItem(
                    value: location.address,
                    child: Text(
                      location.address.toString(),
                      style: bodyTextMedium,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  );
                }).toList();
              }(),
              DropdownMenuItem(
                value: 'add_new_address',
                child: Row(
                  children: [
                    Icon(
                      Icons.add,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Add new address',
                      style: bodyTextMedium.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            value: selectedAddress == '' ? null : selectedAddress,
            onChanged: (value) {
              if (value == 'add_new_address') {
                // Close dropdown first
                FocusScope.of(context).unfocus();

                // Navigate after a small delay to ensure dropdown closes
                Future.delayed(const Duration(milliseconds: 100), () {
                  locator<NavigationServices>()
                      .navigateTo(
                    addAddressPage,
                    arguments: const ProfileAddAddress(isEditing: false),
                  )
                      .then((result) async {
                    await businessController.businessProfileDetails();
                    businessController.userModel.refresh();
                    if (businessController
                        .userModel.value.location.isNotEmpty) {
                      // Select the last added address (assuming it's the newest)
                      var lastLocation =
                          businessController.userModel.value.location.last;
                      selectedAddress = lastLocation.address;
                      var locationDetails = LocationDetails(
                        area: lastLocation.area,
                        city: lastLocation.city,
                        state: lastLocation.state,
                        address: lastLocation.address,
                        country: lastLocation.country,
                        latitude: double.parse(lastLocation.latitude),
                        pinCode: lastLocation.pinCode,
                        longitude: double.parse(lastLocation.longitude),
                        locationId: lastLocation.locationId,
                        subLocality: lastLocation.subLocality,
                      );

                      createEventModel.city = lastLocation.city;
                      createEventModel.locationDetails = locationDetails;
                      eventController.eventDetailsModel.value.address =
                          lastLocation.address;
                    } else {
                      selectedAddress = '';
                    }
                    if (mounted) {
                      setState(() {});
                    }
                  });
                });
                return;
              }

              if (value == 'separator') {
                // Ignore separator selection
                return;
              }

              // Handle regular address selection
              var selectedLocation = businessController.userModel.value.location
                  .firstWhere((location) => location.address == value);

              var locationDetails = LocationDetails(
                  area: selectedLocation.area,
                  city: selectedLocation.city,
                  state: selectedLocation.state,
                  address: selectedLocation.address,
                  country: selectedLocation.country,
                  latitude: double.parse(selectedLocation.latitude),
                  pinCode: selectedLocation.pinCode,
                  longitude: double.parse(selectedLocation.longitude),
                  locationId: selectedLocation.locationId,
                  subLocality: selectedLocation.subLocality);

              createEventModel.city = selectedLocation.city;
              createEventModel.locationDetails = locationDetails;
              eventController.eventDetailsModel.value.address =
                  selectedLocation.address;

              setState(() {
                selectedAddress = value.toString();
              });
            },
          ),
        ],
      ),
    );
  }

  Widget buildTicketTypeSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Ticket Type*", style: body2TextRegular),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      isTicketPaid.value = true;
                      isTicketFree.value = false;
                      createEventModel.ticketType = "paid";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(12),
                      ),
                      color: isTicketPaid.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isTicketPaid.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Paid",
                        style: body2TextMedium.copyWith(
                          fontWeight: isTicketPaid.value
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: isTicketPaid.value
                              ? AppColors.txtprimary
                              : AppColors.txtsecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      isTicketPaid.value = false;
                      isTicketFree.value = true;
                      createEventModel.ticketType = "free";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(12),
                      ),
                      color: isTicketFree.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isTicketFree.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Free",
                        style: body2TextMedium.copyWith(
                          fontWeight: isTicketFree.value
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: isTicketFree.value
                              ? AppColors.txtprimary
                              : AppColors.txtsecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildMultipleTicketPricingSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Do you have multiple ticket pricing?*",
              style: body2TextRegular),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      isMultipleTicketYes.value = true;
                      isMultipleTicketNo.value = false;
                      createEventModel.multiplePricing = "yes";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(8),
                      ),
                      color: isMultipleTicketYes.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isMultipleTicketYes.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Yes",
                        style: body2TextMedium.copyWith(
                          fontWeight: isMultipleTicketYes.value
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: isMultipleTicketYes.value
                              ? AppColors.txtprimary
                              : AppColors.txtsecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      isMultipleTicketYes.value = false;
                      isMultipleTicketNo.value = true;
                      createEventModel.multiplePricing = "no";
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(12),
                      ),
                      color: isMultipleTicketNo.value
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isMultipleTicketNo.value
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "No",
                        style: body2TextMedium.copyWith(
                          fontWeight: isMultipleTicketNo.value
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: isMultipleTicketNo.value
                              ? AppColors.txtprimary
                              : AppColors.txtsecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildSubmitButton() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Row(
      mainAxisAlignment:
          screenWidth <= 820 ? MainAxisAlignment.center : MainAxisAlignment.end,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: SecondaryButton(
            textColor:
                // businessController.userModel.value.kycDone == 0
                //     ? AppColors.ktertiary
                //     :
                AppColors.kprimarycolor,
            backgroundColor:
                // businessController.userModel.value.kycDone == 0
                //     ? AppColors.scaffoldColor
                //     :
                AppColors.kwhite,
            text: 'Save as draft',
            onTap: () async {
              // if (businessController.userModel.value.kycDone == 1) {
              Map<String, dynamic> draftEventPayload = {
                "business_id": int.parse(
                  storage.read("USER_ID"),
                ),
                "title": createEventModel.title,
                "banner_url": _imageUrl,
                "price": createEventModel.price,
                "description": createEventModel.description,
                "duration": createEventModel.duration,
                "cta_url": createEventModel.ctaUrl,
                "event_type": createEventModel.eventType,
                "city": createEventModel.city,
                "location_details": {
                  "area": createEventModel.locationDetails.area,
                  "city": createEventModel.locationDetails.city,
                  "state": createEventModel.locationDetails.state,
                  "address": createEventModel.locationDetails.address,
                  "country": createEventModel.locationDetails.country,
                  "latitude": createEventModel.locationDetails.latitude,
                  "pin_code": createEventModel.locationDetails.pinCode,
                  "longitude": createEventModel.locationDetails.longitude,
                  "location_id": createEventModel.locationDetails.locationId,
                  "sub_locality": createEventModel.locationDetails.subLocality
                },
                "ticket_type": createEventModel.ticketType,
                "cta_mobile": createEventModel.ctaMobile,
                "multiple_pricing": createEventModel.multiplePricing,
                "min_age": createEventModel.minAge,
                "publish": 0,
                "max_age": createEventModel.maxAge,
                "start_date": createEventModel.startDate,
                "end_date": createEventModel.endDate,
                "status": "draft"
              };

              createEvent(draftEventPayload, "draft");
              // }
            },
          ),
        ),
        const Gap(10),
        PrimaryButton(
            text: 'Submit for review',
            textColor:
                //  businessController.userModel.value.kycDone == 0
                //     ? AppColors.ktertiary
                //     :
                AppColors.kwhite,
            backgroundColor:
                // : businessController.userModel.value.kycDone == 0
                //     ? AppColors.scaffoldColor
                //     :
                AppColors.kprimarycolor,
            onTap: () async {
              // if (businessController.userModel.value.kycDone == 1) {
              Map<String, dynamic> reviewEventPayload = {
                "business_id": int.parse(
                  storage.read("USER_ID"),
                ),
                "title": createEventModel.title,
                "banner_url": _imageUrl,
                "price": createEventModel.price,
                "description": createEventModel.description,
                "duration": createEventModel.duration,
                "cta_url": createEventModel.ctaUrl,
                "event_type": createEventModel.eventType,
                "city": createEventModel.city,
                "location_details": {
                  "area": createEventModel.locationDetails.area,
                  "city": createEventModel.locationDetails.city,
                  "state": createEventModel.locationDetails.state,
                  "address": createEventModel.locationDetails.address,
                  "country": createEventModel.locationDetails.country,
                  "latitude": createEventModel.locationDetails.latitude,
                  "pin_code": createEventModel.locationDetails.pinCode,
                  "longitude": createEventModel.locationDetails.longitude,
                  "location_id": createEventModel.locationDetails.locationId,
                  "sub_locality": createEventModel.locationDetails.subLocality
                },
                "ticket_type": createEventModel.ticketType,
                "cta_mobile": createEventModel.ctaMobile,
                "multiple_pricing": createEventModel.multiplePricing,
                "min_age": createEventModel.minAge,
                "publish": 1,
                "max_age": createEventModel.maxAge,
                "start_date": createEventModel.startDate,
                "end_date": createEventModel.endDate,
                "status": "inreview"
              };
              createEvent(reviewEventPayload, "inreview");
            }
            // },
            ),
      ],
    );
  }

  void createEvent(Map<String, dynamic> payload, String type) async {
    if (_formKey.currentState?.validate() ?? false) {
      if (_imageUrl.isEmpty) {
        Get.snackbar(
          "Error",
          "Event banner image is required.",
          snackStyle: SnackStyle.FLOATING,
          backgroundColor: AppColors.errorRed,
          maxWidth: 300,
          colorText: AppColors.kwhite,
        );
        return;
      }
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return ConfirmPopup(
            dialogHeight: type == "inreview" ? 220 : 220,
            dialogWidth: 300,
            title: type == "inreview" ? 'Submit for review' : 'Save as draft',
            message: type == "inreview"
                ? 'Please ensure the details you entered are\ncorrect. Once submitted, your event will be\nreviewed and published upon approval.'
                : 'Are you sure you want to save this as a draft?',
            onConfirm: () async {
              log(type == "inreview" ? "Submit for review" : "Save as draft");
              bool isSuccess = await eventController.createEvent(payload);
              if (isSuccess) {
                await Future.delayed(const Duration(milliseconds: 300))
                    .then((value) {
                  Navigator.pop(Get.context!);
                  Get.snackbar(
                    "Success",
                    type == "inreview"
                        ? "Event submited for review."
                        : "Saved as draft.",
                    snackStyle: SnackStyle.FLOATING,
                    backgroundColor: AppColors.kprimarycolor,
                    maxWidth: 300,
                    colorText: AppColors.kwhite,
                  );
                  String targetTab = type == "draft" ? "draft" : "published";
                  locator<NavigationServices>().goBackWithResult(targetTab);
                });
              } else {
                Get.snackbar(
                  "Failed",
                  type == "inreview"
                      ? "Failed to submit for review. Please try again.."
                      : "Failed to save as draft. Please try again.",
                  snackStyle: SnackStyle.FLOATING,
                  backgroundColor: AppColors.errorRed,
                  maxWidth: 300,
                  colorText: AppColors.kwhite,
                );
              }
            },
            icon: SvgPicture.asset(
              type == "inreview"
                  ? 'assets/icons/Checks.svg'
                  : 'assets/icons/PencilLine.svg',
              height: 50,
              fit: BoxFit.fill,
            ),
            confirmText: type == "inreview" ? 'Submit' : 'Save as draft',
            cancelText: 'Cancel',
          );
        },
      );
    } else {
      Get.snackbar(
        "Error",
        "All field is required.",
        snackStyle: SnackStyle.FLOATING,
        backgroundColor: AppColors.errorRed,
        maxWidth: 300,
        colorText: AppColors.kwhite,
      );
    }
  }

  Future<void> _uploadFileHelper(
      PlatformFile pickedFile, File? file, Uint8List fileBytes) async {
    String fileExtension = pickedFile.extension?.toLowerCase() ?? '';
    List<String> allowedExtensions = ['jpg', 'jpeg', 'png'];
    if (!allowedExtensions.contains(fileExtension)) {
      Get.snackbar(
        "Error",
        "Please upload a valid image file (jpg, jpeg, png).",
        snackStyle: SnackStyle.FLOATING,
        backgroundColor: AppColors.errorRed,
        maxWidth: 300,
        colorText: AppColors.kwhite,
      );
      return;
    }

    if (pickedFile.size > 1 * 1024 * 1024) {
      Get.snackbar(
        "Error",
        "File size exceeds 1 MB limit.",
        snackStyle: SnackStyle.FLOATING,
        backgroundColor: AppColors.errorRed,
        maxWidth: 300,
        colorText: AppColors.kwhite,
      );
      return;
    }

    String contentType = 'image/$fileExtension';
    String filePath = file?.path ?? '';
    log("Starting _uploadFileHelper with fileName: ${pickedFile.name}, filePath: $filePath,Content Type $contentType");
    bool value = await eventController.createFileNameEntry(
        pickedFile.name, contentType, filePath, fileBytes, "banner");
    if (value) {
      String encodedFileName = Uri.encodeComponent(pickedFile.name);
      String newImageUrl =
          "https://profilemedia.s3.ap-south-1.amazonaws.com/$encodedFileName";
      setState(() {
        eventController.uploadedFileName.value = pickedFile.name;
        _imageUrl = newImageUrl;
        createEventModel.bannerUrl = newImageUrl;
        log("_imageUrl set to: $newImageUrl");
      });

      Get.snackbar(
        "Success",
        "Event Banner uploaded successfully",
        snackStyle: SnackStyle.FLOATING,
        backgroundColor: AppColors.kprimarycolor,
        maxWidth: 300,
        colorText: AppColors.kwhite,
      );
    } else {
      log("unable to upload the image");
      Get.snackbar(
        "Failed",
        "Unable to upload event banner... try again later",
        snackStyle: SnackStyle.FLOATING,
        backgroundColor: AppColors.errorRed,
        maxWidth: 300,
        colorText: AppColors.kwhite,
      );
    }
  }
}
