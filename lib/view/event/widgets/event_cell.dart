import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/model/event/event_model.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';

class EventCell extends StatelessWidget {
  const EventCell({
    super.key,
    required this.eventModelObject,
    this.currentTab,
    this.isAdmin = false, required this.eventController,
  });
  final EventModel eventModelObject;
  final String? currentTab;
  final bool isAdmin;
  final EventController eventController;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      child: InkWell(
        onTap: () {
          isAdmin
              ? locator<NavigationServices>().navigateTo(adminEventDetailsPage,
                  arguments: <String, int>{
                      'requestID': eventModelObject.requestID!
                    })
              : locator<NavigationServices>()
                  .navigateTo(eventDetailsRoute, arguments: <String, dynamic>{
                  'eventID': eventModelObject.eventId!,
                  'eventTab': currentTab == "published"
                      ? "published"
                      : currentTab == "draft"
                          ? "draft"
                          : "past"
                }).then((value) {
                  if (currentTab == "published") {
                    eventController.getAllEventList("published");
                  } else if (currentTab == "draft") {
                    eventController.getAllEventList("draft");
                  } else {
                    eventController.getAllEventList("past");
                  }
                });
        },
        child: Container(
          //padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.scaffoldColor,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Stack(
                    children: [
                      CachedNetworkImage(
                        imageUrl: eventModelObject.bannerUrl,
                        width: 120,
                        height: 120,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Image.asset(
                            "assets/png/Banner_Placeholder.png",
                            width: 120,
                            height: 120,
                            fit: BoxFit.cover),
                        errorWidget: (context, url, error) => Image.asset(
                            "assets/png/Banner_Placeholder.png",
                            width: 120,
                            height: 120,
                            fit: BoxFit.cover),
                          
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          height: 26,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.zero,
                            color: eventModelObject.status == 'draft'
                                ? AppColors.kwarning
                                : eventModelObject.isApproved == 1 &&
                                        eventModelObject.status == 'approved'
                                    ? AppColors.lightGreen
                                    : eventModelObject.isApproved == 0 &&
                                            eventModelObject.status ==
                                                'inreview'
                                        ? AppColors.information
                                        : AppColors.lightPink,
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Center(
                            child: Text(
                              eventModelObject.status.capitalizeFirst
                                  .toString().replaceAll("Inreview", "In-review"),
                              style: body3TextMedium,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const Gap(10),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        eventModelObject.title,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: bodyTextMedium,
                      ),
                      const Gap(2),
                      eventModelObject.eventType == "online"
                          ? const EventRowChild(
                              iconPath: "assets/icons/VideoConference.svg",
                              textTitle: "Online")
                          : EventRowChild(
                              iconPath: "assets/svg/MapPin.svg",
                              textTitle: eventModelObject.city),
                      // EventRowChild(
                      //   iconPath: "assets/svg/MapPin.svg",
                      //   textTitle: eventModelObject.city,
                      // ),
                      const Gap(5),
                      EventRowChild(
                        iconPath: "assets/svg/Calendar.svg",
                        textTitle: dateFormat(eventModelObject.startDate),
                        // textTitle: "",
                      ),
                      const Gap(5),
                      EventRowChild(
                        iconPath: "assets/svg/CurrencyInr.svg",
                        textTitle: eventModelObject.price == 0
                            ? "Free"
                            : "${eventModelObject.price} ${eventModelObject.price == 1 ? '' : 'onwards'}",
                      ),

                      // EventRowChild(
                      //   iconPath: "assets/svg/CurrencyInr.svg",
                      //   textTitle: "${eventModelObject.price} onwards",
                      // ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class EventRowChild extends StatelessWidget {
  const EventRowChild(
      {super.key, required this.iconPath, required this.textTitle});
  final String iconPath;
  final String textTitle;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(
          iconPath,
          height: 16,
        ),
        const Gap(3),
        Text(textTitle,
            style: body3TextRegular.copyWith(color: AppColors.txtsecondary))
      ],
    );
  }
}

class EventDetailsRowChild extends StatelessWidget {
  const EventDetailsRowChild(
      {super.key, required this.iconPath, required this.textTitle});
  final String iconPath;
  final String textTitle;

  @override
  Widget build(BuildContext context) {
    return Row(
              mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          iconPath,
          height: 24,
          width: 24,
        ),
        const Gap(3),
        Text(textTitle,
            style: bodyTextRegular.copyWith(color: AppColors.txtsecondary,overflow: TextOverflow.ellipsis,),  softWrap: false,)
      ],
    );
  }
}

class EventColumChild extends StatelessWidget {
  const EventColumChild(
      {super.key, required this.title, required this.subTitle,this.onTap});
  final String title;
  final String subTitle;
final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    double scrrenWidth = MediaQuery.of(context).size.width;

    return Padding(
      padding: scrrenWidth <= 720
          ? EdgeInsets.zero
          : const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "$title:",
            style: bodyTextRegular,
          ),
          const Gap(3),
          InkWell(
            onTap: onTap,
            child: Text(subTitle,
                style: bodyTextMedium,
                maxLines: 4,
                overflow: TextOverflow.ellipsis),
          )
        ],
      ),
    );


  }
}
