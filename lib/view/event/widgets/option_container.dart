import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/res/app_color.dart';

class OptionContainer extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onTap;

  const OptionContainer(
      {required this.text,
      required this.isSelected,
      required this.onTap,
      super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        focusColor: Colors.transparent,  
        hoverColor: Colors.transparent,
        child: Container(
          height: 37,
          width: Get.width,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.kwhite : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected
                  ? AppColors.kprimarycolor
                  : AppColors.scaffoldColor,
            ),
          ),
          child: Center(
            child: Text(
              text,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected
                    ? AppColors.kprimarycolor
                    : AppColors.txtsecondary,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
