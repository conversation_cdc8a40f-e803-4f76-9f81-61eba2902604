import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/home/<USER>';
import 'package:parenthing_dashboard/admin_dashboard/model/home/<USER>';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';
import 'package:parenthing_dashboard/view/common_widgets/loader.dart';


class HomePageController extends GetxController {
  String fileName = "HomePageController";
  RxBool isGraphDataLoading = true.obs;
  RxBool isNewBusinessAdded = false.obs;
  var homePageGraphData = HomeGraphModel(
      analyticsResult: AnalyticsResult(
          totalEvents: TotalEvents(),
          totalClasses: TotalClasses(),
          parentsDetails: ParentsDetails(),
          businessDetails: BusinessDetails(),
          parentsAnalytics: [],
          businessAnalytics: [])).obs;

  RxBool isAnalyticsDataLoading = true.obs;
  var analyticsGraphData = AnalyticsModel(
          analyticsResult: AnalyticsGraphResult(
              eventsAnalytics: [],
              genderAnalytics: GenderAnalytics(),
              classesAnalytics: [],
              parentsAnalytics: [],
              businessAnalytics: [],
              connectionsAnalytics: [],
              userStatusAnalytics: UserStatusAnalytics()))
      .obs;
  String userID = "0";

late final ApiController apiController;

  @override
  void onInit() {
    apiController = Get.find<ApiController>();
    super.onInit();
  }

  Future<bool> getGraphData(String cityName, String periodType) async {
    userID = storage.read("USER_ID");
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "city": cityName,
        "period_type": periodType,
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.homePageGraph,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          homePageGraphData.value = HomeGraphModel.fromJson(data);
          isGraphDataLoading.value = false;
          return true;
        } else {
          homePageGraphData.value = HomeGraphModel(
              analyticsResult: AnalyticsResult(
                  totalEvents: TotalEvents(),
                  totalClasses: TotalClasses(),
                  parentsDetails: ParentsDetails(),
                  businessDetails: BusinessDetails(),
                  parentsAnalytics: [],
                  businessAnalytics: []));
          isGraphDataLoading.value = false;
          return false;
        }
      } else {
        homePageGraphData.value = HomeGraphModel(
            analyticsResult: AnalyticsResult(
                totalEvents: TotalEvents(),
                totalClasses: TotalClasses(),
                parentsDetails: ParentsDetails(),
                businessDetails: BusinessDetails(),
                parentsAnalytics: [],
                businessAnalytics: []));
        isGraphDataLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      homePageGraphData.value = HomeGraphModel(
          analyticsResult: AnalyticsResult(
              totalEvents: TotalEvents(),
              totalClasses: TotalClasses(),
              parentsDetails: ParentsDetails(),
              businessDetails: BusinessDetails(),
              parentsAnalytics: [],
              businessAnalytics: []));
      isGraphDataLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>homePageController getGraphData $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAnalyticsGraphData(String cityName, String periodType,
      String classType, String eventType) async {
    userID = storage.read("USER_ID");
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "period_type": periodType,
        "city": cityName,
        "class_type": classType,
        "event_type": eventType
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.analyticsPageGraph,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          analyticsGraphData.value = AnalyticsModel.fromJson(data);
          isAnalyticsDataLoading.value = false;
          return true;
        } else {
          analyticsGraphData.value = AnalyticsModel(
              analyticsResult: AnalyticsGraphResult(
                  eventsAnalytics: [],
                  genderAnalytics: GenderAnalytics(),
                  classesAnalytics: [],
                  parentsAnalytics: [],
                  businessAnalytics: [],
                  connectionsAnalytics: [],
                  userStatusAnalytics: UserStatusAnalytics()));
          isAnalyticsDataLoading.value = false;
          return false;
        }
      } else {
        analyticsGraphData.value = AnalyticsModel(
            analyticsResult: AnalyticsGraphResult(
                eventsAnalytics: [],
                genderAnalytics: GenderAnalytics(),
                classesAnalytics: [],
                parentsAnalytics: [],
                businessAnalytics: [],
                connectionsAnalytics: [],
                userStatusAnalytics: UserStatusAnalytics()));
        isAnalyticsDataLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      analyticsGraphData.value = AnalyticsModel(
          analyticsResult: AnalyticsGraphResult(
              eventsAnalytics: [],
              genderAnalytics: GenderAnalytics(),
              classesAnalytics: [],
              parentsAnalytics: [],
              businessAnalytics: [],
              connectionsAnalytics: [],
              userStatusAnalytics: UserStatusAnalytics()));
      isAnalyticsDataLoading.value = false;
      if (kDebugMode) {
        print(
            "$fileName=>homePageController getAnalyticsGraphData $e \n  $stackTrace");
      }
      return false;
    }
  }

  //! create event fun
  Future<bool> createNewBusinessUser(Map<String, dynamic> payload) async {
    try {
      Loader.showLoading();
      isNewBusinessAdded.value = true;
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.addBusinessUrl,
        payload: payload,
      );
      var jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        await Future.delayed(const Duration(milliseconds: 300));
        Loader.hideLoading();
        isNewBusinessAdded.value = false;
        return true;
      } else {
        Loader.hideLoading();
        isNewBusinessAdded.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      Loader.hideLoading();
      isNewBusinessAdded.value = false;
      if (kDebugMode) {
        print("=>fun createNewBusinessUser $e \n  $stackTrace");
      }
      return false;
    }
  }
}
