import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_home_controller.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import "package:http/browser_client.dart" as browserhttp;
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:uuid/uuid.dart';
import 'package:http/http.dart' as http;

class BusinessOnBoardForm extends StatefulWidget {
  const BusinessOnBoardForm({super.key});

  @override
  State<BusinessOnBoardForm> createState() => _BusinessOnBoardFormState();
}

class _BusinessOnBoardFormState extends State<BusinessOnBoardForm> {
  String businessFullName = '',
      businessPhoneNumber = '',
      businessType = '',
      businessDescription = '';
  final GlobalKey<FormState> addNewBusinessForm = GlobalKey<FormState>();
  bool isOfflineSelected = false;
  bool isOnlineSelected = false;
  bool isOfflineOnlineSelected = false;
  bool isOthersSelected = false;
  final HomePageController homePageController = Get.find<HomePageController>();
  final ApiController apiController = Get.find<ApiController>();
  // String sessionToken = '';
  String googleApiKey = "AIzaSyCSP2Se7lLMrsw2aNy4cdqHb3P0BPcZljs";
  String area = '',
      city = '',
      state = '',
      address = '',
      contry = '',
      subLocality = '';
  String pinCode = '';
  double latitudeValue = 0.0, longitudeValue = 0.0;
  GoogleMapController? mapController;
  LatLng? mylatlong;
  CameraPosition? cameraPosition;
  String addressTitle = '';
  List<Placemark> placemarks = [];
  String currentAddress = '';
  Map<String, dynamic> addressPayload = {};
  late TextEditingController _addressController;
  late TextEditingController _titleController;
  final MarkerId _markerId = const MarkerId('1');
  final Map<MarkerId, Marker> _markers = {};
  var uuid = const Uuid();
  String sessionToken = '122344';

  late TextEditingController _searchController;
  Timer? debounceTimer;
  String searchText = '';
  List<dynamic> placeList = [];

  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;
  }

  @override
  void initState() {
    getLocationPermission();
    sessionToken = uuid.v4();
    _titleController = TextEditingController();
    _addressController = TextEditingController();
    _searchController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _addressController.dispose();
    _titleController.dispose();
    _searchController.dispose();
  }

  void _addMarker(LatLng latLong) {
    setState(() {
      _markers.clear();
      _markers[_markerId] = Marker(
        markerId: _markerId,
        position: latLong,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      );
    });
  }

  Future<void> getLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(Get.context!).showSnackBar(
          const SnackBar(
            content: Text(
                'Location permissions are permanently denied. Please enable location access in your device settings.'),
          ),
        );
        return;
      }
    } catch (e, stackTrace) {
      log('error: $e \n $stackTrace');
    }
  }

  Future<void> getSuggestion(String input) async {
    if (input.isEmpty) {
      setState(() {
        placeList = [];
      });
      return;
    }

    const String apiUrl =
        'https://stag-api.parenthingapp.co.in/dashboard/business/get_address';

    try {
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          "input": input,
          "map_api_key": "AIzaSyCSP2Se7lLMrsw2aNy4cdqHb3P0BPcZljs",
          "sessionToken": sessionToken,
        }),
      );

      log('Response status code: ${response.statusCode}');
      log(response.body);

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['success'] == true && jsonResponse['data'] != null) {
          setState(() {
            placeList = List<Map<String, dynamic>>.from(jsonResponse['data']);
            if (kDebugMode) {
              print("placelist $placeList");
            }
          });
        } else {
          setState(() {
            placeList = [];
          });
        }
      } else {
        log('location data not found');
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${response.statusCode}'),
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error making API call: $e');
      }
    }
  }

  Future<Map<String, dynamic>?> getPlaceDetails(String placeId) async {
    const String apiUrl =
        'https://stag-api.parenthingapp.co.in/dashboard/business/get_address';

    try {
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          "place_id": placeId,
          "map_api_key": AppUrl.googleMapsAPIKEY,
          "sessionToken": sessionToken,
        }),
      );
      if (kDebugMode) {
        print("location_with_placeID:::=> ${response.body}");
      }
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['success'] == true && jsonResponse['data'] != null) {
          return jsonResponse['data'];
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching place details: $e');
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Container(
            padding: const EdgeInsets.all(16.0),
            width: Get.width / 1.5,
            decoration: BoxDecoration(
                border: Border.all(color: AppColors.backcolor, width: 1.0),
                borderRadius: BorderRadius.circular(16.0)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: () {
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return CustomDialog(
                                onConfirmTxt: "Yes, leave",
                                onCancelText: "No",
                                title: 'Leave this page?',
                                content:
                                    'Are you sure you want to leave this page? All field details will be discarded',
                                image: "assets/icons/WarningCircle.svg",
                                onConfirm: () {
                                  Navigator.of(context).pop();
                                  locator<NavigationServices>().goBack();
                                },
                                onCancel: () {
                                  Navigator.of(context).pop();
                                },
                              );
                            },
                          );
                        },
                        child: SvgPicture.asset('assets/icons/arrow-left.svg',
                            fit: BoxFit.fill, height: 32, width: 32),
                      ),
                      mdWidth,
                      Text("Add new business", style: heading2TextRegular),
                    ],
                  ),
                ),
                const Divider(
                  thickness: 1.0,
                  color: AppColors.kgrey,
                ),
                const Gap(20),
                Form(
                  key: addNewBusinessForm,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text('Business name*', style: body2TextRegular),
                      CustomTextFormField(
                        onChanged: (String value) {
                          setState(() {
                            businessFullName = value;
                          });
                        },
                        hintText: "Business name",
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Enter business name.';
                          }
                          return null;
                        },
                      ),
                      const Gap(20),
                      Text('Business phone*', style: body2TextRegular),
                      CustomTextFormField(
                        onChanged: (String value) {
                          setState(() {
                            businessPhoneNumber = value;
                          });
                        },
                        maxLines: 1,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        keyboardType: TextInputType.phone,
                        maxLength: 10,
                        hintText: "Business phone number",
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Enter business phone number.';
                          }
                          return null;
                        },
                      ),
                      const Gap(20),
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('What does your business do?*',
                                    style: body2TextRegular),
                                kSmHeight,
                                screenWidth <= 820
                                    ? Row(
                                        children: [
                                          Expanded(
                                            child: InkWell(
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected = true;
                                                  isOnlineSelected = false;
                                                  isOfflineOnlineSelected =
                                                      false;
                                                  isOthersSelected = false;
                                                  businessType =
                                                      'conduct events for kids';
                                                });
                                              },
                                              child: Container(
                                                height: 40,
                                                padding:
                                                    const EdgeInsets.all(8),
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius
                                                          .horizontal(
                                                    left: Radius.circular(8),
                                                    right: Radius.zero,
                                                  ),
                                                  color: isOfflineSelected
                                                      ? AppColors.kwhite
                                                      : AppColors.backcolor,
                                                  border: Border.all(
                                                    color: isOfflineSelected
                                                        ? AppColors.kblack
                                                        : AppColors.lgrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Events",
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 2,
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                      color: isOfflineSelected
                                                          ? AppColors.txtprimary
                                                          : AppColors
                                                              .txtsecondary,
                                                      fontWeight:
                                                          isOfflineSelected
                                                              ? FontWeight.w600
                                                              : FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: InkWell(
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected = false;
                                                  isOnlineSelected = true;
                                                  isOfflineOnlineSelected =
                                                      false;
                                                  isOthersSelected = false;
                                                  businessType =
                                                      'classes for kids';
                                                });
                                              },
                                              child: Container(
                                                height: 40,
                                                padding:
                                                    const EdgeInsets.all(8),
                                                decoration: BoxDecoration(
                                                  color: isOnlineSelected
                                                      ? AppColors.kwhite
                                                      : AppColors.backcolor,
                                                  border: Border.all(
                                                    color: isOnlineSelected
                                                        ? AppColors.kblack
                                                        : AppColors.lgrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Classes",
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 2,
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                      color: isOnlineSelected
                                                          ? AppColors.txtprimary
                                                          : AppColors
                                                              .txtsecondary,
                                                      fontWeight:
                                                          isOnlineSelected
                                                              ? FontWeight.w600
                                                              : FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: InkWell(
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected = false;
                                                  isOnlineSelected = false;
                                                  isOfflineOnlineSelected =
                                                      true;
                                                  isOthersSelected = false;
                                                  businessType = 'both';
                                                });
                                              },
                                              child: Container(
                                                height: 40,
                                                decoration: BoxDecoration(
                                                  color: isOfflineOnlineSelected
                                                      ? AppColors.kwhite
                                                      : AppColors.backcolor,
                                                  border: Border.all(
                                                    color:
                                                        isOfflineOnlineSelected
                                                            ? AppColors.kblack
                                                            : AppColors.lgrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Both",
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                      color:
                                                          isOfflineOnlineSelected
                                                              ? AppColors
                                                                  .txtprimary
                                                              : AppColors
                                                                  .txtsecondary,
                                                      fontWeight:
                                                          isOfflineOnlineSelected
                                                              ? FontWeight.w600
                                                              : FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: InkWell(
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected = false;
                                                  isOnlineSelected = false;
                                                  isOfflineOnlineSelected =
                                                      false;
                                                  isOthersSelected = true;
                                                  businessType = 'Others';
                                                });
                                              },
                                              child: Container(
                                                height: 40,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius
                                                          .horizontal(
                                                    left: Radius.zero,
                                                    right: Radius.circular(8),
                                                  ),
                                                  color: isOthersSelected
                                                      ? AppColors.kwhite
                                                      : AppColors.backcolor,
                                                  border: Border.all(
                                                    color: isOthersSelected
                                                        ? AppColors.kblack
                                                        : AppColors.lgrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Others",
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                      color: isOthersSelected
                                                          ? AppColors.txtprimary
                                                          : AppColors
                                                              .txtsecondary,
                                                      fontWeight:
                                                          isOthersSelected
                                                              ? FontWeight.w600
                                                              : FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      )
                                    : Row(
                                        children: [
                                          Expanded(
                                            child: InkWell(
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected = true;
                                                  isOnlineSelected = false;
                                                  isOfflineOnlineSelected =
                                                      false;
                                                  isOthersSelected = false;
                                                  businessType =
                                                      'Conduct events for kids';
                                                });
                                              },
                                              child: Container(
                                                height: 40,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius
                                                          .horizontal(
                                                    left: Radius.circular(8),
                                                    right: Radius.zero,
                                                  ),
                                                  color: isOfflineSelected
                                                      ? AppColors.kwhite
                                                      : AppColors.backcolor,
                                                  border: Border.all(
                                                    color: isOfflineSelected
                                                        ? AppColors.kblack
                                                        : AppColors.lgrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Conduct events for kids",
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                      color: isOfflineSelected
                                                          ? AppColors.txtprimary
                                                          : AppColors
                                                              .txtsecondary,
                                                      fontWeight:
                                                          isOfflineSelected
                                                              ? FontWeight.w600
                                                              : FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: InkWell(
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected = false;
                                                  isOnlineSelected = true;
                                                  isOfflineOnlineSelected =
                                                      false;
                                                  isOthersSelected = false;
                                                  businessType =
                                                      'Classes for kids';
                                                });
                                              },
                                              child: Container(
                                                height: 40,
                                                decoration: BoxDecoration(
                                                  color: isOnlineSelected
                                                      ? AppColors.kwhite
                                                      : AppColors.backcolor,
                                                  border: Border.all(
                                                    color: isOnlineSelected
                                                        ? AppColors.kblack
                                                        : AppColors.lgrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Classes for kids",
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                      color: isOnlineSelected
                                                          ? AppColors.txtprimary
                                                          : AppColors
                                                              .txtsecondary,
                                                      fontWeight:
                                                          isOnlineSelected
                                                              ? FontWeight.w600
                                                              : FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: InkWell(
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected = false;
                                                  isOnlineSelected = false;
                                                  isOfflineOnlineSelected =
                                                      true;
                                                  isOthersSelected = false;
                                                  businessType = 'both';
                                                });
                                              },
                                              child: Container(
                                                height: 40,
                                                decoration: BoxDecoration(
                                                  color: isOfflineOnlineSelected
                                                      ? AppColors.kwhite
                                                      : AppColors.backcolor,
                                                  border: Border.all(
                                                    color:
                                                        isOfflineOnlineSelected
                                                            ? AppColors.kblack
                                                            : AppColors.lgrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Both",
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                      color:
                                                          isOfflineOnlineSelected
                                                              ? AppColors
                                                                  .txtprimary
                                                              : AppColors
                                                                  .txtsecondary,
                                                      fontWeight:
                                                          isOfflineOnlineSelected
                                                              ? FontWeight.w600
                                                              : FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: InkWell(
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected = false;
                                                  isOnlineSelected = false;
                                                  isOfflineOnlineSelected =
                                                      false;
                                                  isOthersSelected = true;
                                                  businessType = 'Others';
                                                });
                                              },
                                              child: Container(
                                                height: 40,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius
                                                          .horizontal(
                                                    left: Radius.zero,
                                                    right: Radius.circular(8),
                                                  ),
                                                  color: isOthersSelected
                                                      ? AppColors.kwhite
                                                      : AppColors.backcolor,
                                                  border: Border.all(
                                                    color: isOthersSelected
                                                        ? AppColors.kblack
                                                        : AppColors.lgrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Others",
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                      color: isOthersSelected
                                                          ? AppColors.txtprimary
                                                          : AppColors
                                                              .txtsecondary,
                                                      fontWeight:
                                                          isOthersSelected
                                                              ? FontWeight.w600
                                                              : FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                kMinHeight,
                                isOthersSelected
                                    ? Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                              'Tell us more about your business (250 max characters)*',
                                              style: body2TextRegular),
                                          kSmHeight,
                                          CustomTextFormField(
                                            maxLength: 250,
                                            maxLines: 30,
                                            onChanged: (val) {
                                              businessDescription = val;
                                            },
                                            initialValue: businessType,
                                            hintText:
                                                "e.g. Workshops, seminars",
                                            // validator: (value) {
                                            //   if (value == null || value.isEmpty) {
                                            //     return 'Please enter your office address';
                                            //   }
                                            //   return null;
                                            // },
                                          ),
                                        ],
                                      )
                                    : const SizedBox(),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const Gap(20),
                      SizedBox(
                        height: 250,
                        width: Get.width,
                        child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: buildMapContainer()),
                      ),
                      const Gap(20),
                      Text('Complete Address* (Tap on map or search)',
                          style: body2TextRegular),
                      CustomTextFormField(
                        controller: _addressController,
                        onChanged: (val) {
                          setState(() {
                            searchText = val;
                            log('User searched in search field: $val');
                          });
                          getSuggestion(val);
                        },
                        maxLines: 5,
                        hintText: "Street, Area, city",
                        validator: (value) {
                          if (value == null ||
                              value.isEmpty ||
                              latitudeValue == 0.0 ||
                              longitudeValue == 0.0) {
                            return 'Please select a location from the map or search for an address';
                          }
                          return null;
                        },
                      ),
                      const Gap(4),
                      if (placeList.isNotEmpty)
                        Container(
                          height: 150,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.3),
                                spreadRadius: 2,
                                blurRadius: 5,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: ListView.builder(
                            itemCount: placeList.length,
                            itemBuilder: (context, index) {
                              final prediction = placeList[index];
                              return ListTile(
                                title: Text(prediction['structured_formatting']
                                    ['main_text']),
                                subtitle: Text(
                                    prediction['structured_formatting']
                                        ['secondary_text']),
                                onTap: () async {
                                  setState(() {
                                    _searchController.text =
                                        prediction['structured_formatting']
                                            ['main_text'];
                                    placeList = [];
                                  });

                                  final placeId = prediction['place_id'];
                                  final details =
                                      await getPlaceDetails(placeId);

                                  if (details != null) {
                                    final lat =
                                        details['geometry']['location']['lat'];
                                    final lng =
                                        details['geometry']['location']['lng'];

                                    LatLng newPosition = LatLng(lat, lng);
                                    _addMarker(newPosition);

                                    mapController?.animateCamera(
                                        CameraUpdate.newLatLngZoom(
                                            newPosition, 15));

                                    await _getUpdatedAddress(
                                        latitude: lat, longitude: lng);
                                  }
                                },
                              );
                            },
                          ),
                        ),
                      const Gap(20),
                    ],
                  ),
                ),
                SizedBox(
                  width: 300,
                  child: PrimaryButton(
                      text: "Add business",
                      onTap: () {
                        if (addNewBusinessForm.currentState?.validate() ??
                            false) {
                          if (latitudeValue == 0.0 || longitudeValue == 0.0) {
                            _showErrorDialog(
                                'Please select a location from the map or search for an address.');
                            return;
                          } else {
                            Map<String, dynamic> payload = {
                              "business_name": businessFullName,
                              "business_type": businessType,
                              "business_phone": businessPhoneNumber,
                              "business_description": businessDescription,
                              "location": {
                                "lat": latitudeValue,
                                "long": longitudeValue,
                                "address": _addressController.text,
                                "area": area,
                                "pin_code": int.parse(pinCode),
                                "city": city,
                                "title": "Office",
                                "country": contry,
                                "state": state,
                                "sub_locality": subLocality
                              }
                            };
                            log(
                                'new business payload :: ${json.encode(payload)}');
                            homePageController
                                .createNewBusinessUser(payload)
                                .then((value) => {
                                      if (value == true)
                                        {
                                          Get.snackbar(
                                              '', "New business added.",
                                              colorText: Colors.white,
                                              backgroundColor: Colors.green),
                                          locator<NavigationServices>().goBack()
                                        }
                                    });
                          }
                        }
                      }),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Error'),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Widget buildMapContainer() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: GoogleMap(
            initialCameraPosition: cameraPosition ??
                const CameraPosition(
                    target: LatLng(19.2183, 72.9781), zoom: 14.0),
            onMapCreated: _onMapCreated,
            mapType: MapType.terrain,
            myLocationEnabled: true,
            markers: Set<Marker>.of(_markers.values),
            onTap: (latLong) {
              log('lat:::${latLong.latitude},${latLong.longitude}');
              _addMarker(latLong);
              _getUpdatedAddress(
                  latitude: latLong.latitude, longitude: latLong.longitude);
            },
            onCameraMove: (CameraPosition newPosition) {
              log("ON MAP");
            },
          )),
    );
  }

  Future<void> _getUpdatedAddress(
      {required double latitude, required double longitude}) async {
    final client = browserhttp.BrowserClient();
    client.withCredentials = true;
    try {
      Map<String, dynamic> location = {
        "lat": latitude,
        "lng": longitude,
        "map_api_key": AppUrl.googleMapsAPIKEY
      };

      final value = await apiController.apiHelperFn(
          apiUrl: AppUrl.getPlaceFromMap, payload: location);
      final jsonMap = json.decode(value);
      if (jsonMap["success"] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];

          if (data.isNotEmpty) {
            final formattedAddress = data[0]['formatted_address'];
            setState(() {
              _addressController.text = formattedAddress;
              latitudeValue = data[0]['geometry']['location']['lat'];
              longitudeValue = data[0]['geometry']['location']['lng'];
            });
            final addressComponents = data[0]['address_components'];
            for (var component in addressComponents) {
              final types = component['types'];
              if (types.contains('locality')) {
                city = component['long_name'];
              } else if (types.contains('administrative_area_level_1')) {
                state = component['long_name'];
              } else if (types.contains('country')) {
                contry = component['long_name'];
              } else if (types.contains('postal_code')) {
                pinCode = component['long_name'] ?? "";
              } else if (types.contains('sublocality')) {
                subLocality = component['long_name'];
              } else if (types.contains('route')) {
                area = component['long_name'];
              }
            }
            // setState(() {});
          } else {
            if (kDebugMode) {
              print("error while loading data");
            }
            //ErrorPopup.showErrorDialog(context, "Something went wrong!");
          }
        } else {
          log("Invalid data format");
        }
      } else {
        log("API response not successful: ${value?['msg']}");
      }
    } catch (error, stackTrace) {
      if (kDebugMode) {
        print("getHomePageImages error: $error \n$stackTrace");
      }
    }
  }
}
