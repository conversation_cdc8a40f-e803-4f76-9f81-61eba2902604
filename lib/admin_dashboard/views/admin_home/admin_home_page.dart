import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_business_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_home_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/admin_home_bar_chart.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/admin_home_line_chart.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/value_notifier.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';

class AdminHomePage extends StatefulWidget {
  const AdminHomePage({super.key});

  @override
  State<AdminHomePage> createState() => _AdminHomePageState();
}

class _AdminHomePageState extends State<AdminHomePage> {
  final AdminBusinessController adminBusinessVM =  Get.find<AdminBusinessController>();
  final HomePageController homePageController = Get.find<HomePageController>();
  final AdminUserController adminUserController = Get.find<AdminUserController>();
  RxString selectedCity = "all cities".obs;

  @override
  void initState() {
    super.initState();
    adminBusinessVM.getAdminPendingReview();
    adminUserController.getAdminDetails();
  }

  @override
  void dispose() {
    super.dispose();
    adminBusinessVM.getAdminPendingReview();
    adminUserController.getAdminDetails();
  }

  void updateGraphData() {
    homePageController.getGraphData(selectedCity.value, 'weekly');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Quick actions",
                    style: heading2TextRegular,
                  ),
                  const Gap(14),
                  Row(
                    children: [
                      Bounceable(
                        onTap: () {
                          if (adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Creator" || adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Admin" ||adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Moderator" ||
                                  adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Super Admin") {
                                  locator<NavigationServices>()
                              .navigateTo(businessOnBoardForm);
                                selectedPageNotifier.value = 'Home';
                              } else {
                                Get.snackbar(
                                  'Access Denied',
                                  "You don't have to access add business.",
                                  snackPosition: SnackPosition.TOP,
                                  backgroundColor: AppColors.errorRed,
                                  maxWidth: 300,
                                  colorText: AppColors.kwhite,
                                );
                              }
                        
                        },
                        child: const QuickActionContainer(
                          iconPath: 'assets/svg/new_business.svg',
                          menuTitle: 'Add a business',
                        ),
                      ),
                      const Gap(20),
                      Bounceable(
                        onTap: () {
                          if (adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Creator" || adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Admin" ||adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Moderator" ||
                                  adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Super Admin") {
                             
                          locator<NavigationServices>()
                              .navigateTo(adminCreateEventDetailsPage);
                                selectedPageNotifier.value = 'Home';
                              } else {
                                Get.snackbar(
                                  'Access Denied',
                                  "You don't have access to create events.",
                                  snackPosition: SnackPosition.TOP,
                                  backgroundColor: AppColors.errorRed,
                                  maxWidth: 300,
                                  colorText: AppColors.kwhite,
                                );
                              }
                        
                        },
                        child: const QuickActionContainer(
                          iconPath: 'assets/svg/Event__Inactive.svg',
                          menuTitle: 'Create an Event',
                        ),
                      ),
                    ],
                  )
                ],
              ),
              const Gap(40),
              Obx(
                () => columWidget(
                  "Pending Review",
                  HomePageOverViewSection(
                    data: [
                      {
                        'iconPath': 'assets/icons/admin_kyc.svg',
                        'title': 'KYC verifications',
                        'subtitle': 'Business',
                        'pendingCount': adminBusinessVM
                            .pendingReviewList.value.kycPendingCount,
                        'parents': false,
                      },
                      {
                        'iconPath': 'assets/icons/Event__Active.svg',
                        'title': 'Event to review',
                        'subtitle': 'Business',
                        'pendingCount': adminBusinessVM
                            .pendingReviewList.value.eventReviewPending,
                        'parents': false,
                      },
                      {
                        'iconPath': 'assets/icons/Classes_Active.svg',
                        'title': 'Classes to review',
                        'subtitle': 'Business',
                        'pendingCount': adminBusinessVM
                            .pendingReviewList.value.classesReviewPending,
                        'parents': false,
                      },
                      {
                        'iconPath': 'assets/icons/Flag.svg',
                        'title': 'Reported events',
                        'subtitle': 'Business',
                        'pendingCount': adminBusinessVM
                            .pendingReviewList.value.reportedEvents,
                        'parents': false,
                      },
                      {
                        'iconPath': 'assets/icons/Flag.svg',
                        'title': 'Reported classes',
                        'subtitle': 'Business',
                        'pendingCount': adminBusinessVM
                            .pendingReviewList.value.reportedClasses,
                        'parents': false,
                      },
                      {
                        'iconPath': 'assets/icons/Flag.svg',
                        'title': 'Reported profiles',
                        'subtitle': 'Parents',
                        'pendingCount': adminBusinessVM
                            .pendingReviewList.value.reportedProfiles,
                        'parents': true,
                      },
                    ],
                    onTapGenerator: (index, data) {
                      return () {
                        log('Card at index $index was tapped');
                        String? title = data[index]['title'] as String?;
                        if (title != null) {
                          switch (title) {
                            case 'KYC verifications':
                              if (adminUserController
                                          .adminDetailsModel.value.position ==
                                      "KYC" ||
                                  adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Super Admin") {
                                locator<NavigationServices>()
                                    .navigateTo(adminBusinessRoute,   arguments: {'initialIndex': 1},);
                                selectedPageNotifier.value = 'Business';
                              } else {
                                Get.snackbar(
                                  'Access Denied',
                                  "You don't have access for kyc.",
                                  snackPosition: SnackPosition.TOP,
                                  backgroundColor: AppColors.errorRed,
                                  maxWidth: 300,
                                  colorText: AppColors.kwhite,
                                );
                              }
                              // selectedPageNotifier.value = 'Business';
                              // locator<NavigationServices>().navigateTo(
                              //   adminBusinessRoute,
                              //   arguments: {'initialIndex': 1},
                              // );
                              break;
                            case 'Event to review':
                              if (adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Events" ||
                                  adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Super Admin") {
                                locator<NavigationServices>()
                                    .navigateTo(adminBusinessRoute,  arguments: {'initialIndex': 2});
                                selectedPageNotifier.value = 'Business';
                              } else {
                                Get.snackbar(
                                  'Access Denied',
                                  "You don't have access for events.",
                                  snackPosition: SnackPosition.TOP,
                                  backgroundColor: AppColors.errorRed,
                                  maxWidth: 300,
                                  colorText: AppColors.kwhite,
                                );
                              }

                              break;
                            case 'Classes to review':
                              if (adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Classes" ||
                                  adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Super Admin") {
                                locator<NavigationServices>()
                                    .navigateTo(adminBusinessRoute,  arguments: {'initialIndex': 3});
                                selectedPageNotifier.value = 'Business';
                              } else {
                                Get.snackbar(
                                  'Access Denied',
                                  "You don't have access for classes.",
                                  snackPosition: SnackPosition.TOP,
                                  backgroundColor: AppColors.errorRed,
                                  maxWidth: 300,
                                  colorText: AppColors.kwhite,
                                );
                              }
                              // selectedPageNotifier.value = 'Business';
                              // locator<NavigationServices>().navigateTo(
                              //   adminBusinessRoute,
                              //   arguments: {'initialIndex': 3},
                              // );
                              break;
                            case 'Reported events':
                              if (adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Reports" ||
                                  adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Super Admin") {
                                locator<NavigationServices>().navigateTo(
                                  adminBusinessRoute,
                                  arguments: {
                                    'type': 'events',
                                    'initialIndex': 4
                                  },
                                );
                                selectedPageNotifier.value = 'Business';
                              } else {
                                Get.snackbar(
                                  'Access Denied',
                                  "You don't have access for reported events.",
                                  snackPosition: SnackPosition.TOP,
                                  backgroundColor: AppColors.errorRed,
                                  maxWidth: 300,
                                  colorText: AppColors.kwhite,
                                );
                              }
                              // selectedPageNotifier.value = 'Business';
                              // locator<NavigationServices>().navigateTo(
                              //   adminBusinessRoute,
                              //   arguments: {
                              //     'initialIndex': 4,
                              //     'type': 'events',
                              //   },
                              // );
                              break;
                            case 'Reported classes':
                              if (adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Reports" ||
                                  adminUserController
                                          .adminDetailsModel.value.position ==
                                      "Super Admin") {
                                locator<NavigationServices>().navigateTo(
                                  adminBusinessRoute,
                                  arguments: {
                                    'type': 'classes',
                                      'initialIndex': 4
                                  },
                                );
                                selectedPageNotifier.value = 'Business';
                              } else {
                                Get.snackbar(
                                  'Access Denied',
                                  "You don't have access for reported classes.",
                                  snackPosition: SnackPosition.TOP,
                                  backgroundColor: AppColors.errorRed,
                                  maxWidth: 300,
                                  colorText: AppColors.kwhite,
                                );
                              }
                              // selectedPageNotifier.value = 'Business';
                              // locator<NavigationServices>().navigateTo(
                              //   adminBusinessRoute,
                              //   arguments: {
                              //     'initialIndex': 4,
                              //     'type': 'classes'
                              //   },
                              // );
                              break;
                            case 'Reported profiles':
                              if (
                                adminUserController
                                      .adminDetailsModel.value.position ==
                                  "Reports" ||
                                adminUserController
                                      .adminDetailsModel.value.position ==
                                  "Super Admin" || adminUserController
                                      .adminDetailsModel.value.position ==
                                  "Admin") {
                             selectedPageNotifier.value = 'Parents';
                            locator<NavigationServices>().navigateTo(
                              adminParentsRoute,
                              arguments: {'initialIndex': 1},
                            );
                              } else {
                                Get.snackbar(
                                  'Access Denied',
                                  "You don't have access for reported parents.",
                                  snackPosition: SnackPosition.TOP,
                                  backgroundColor: AppColors.errorRed,
                                  maxWidth: 300,
                                  colorText: AppColors.kwhite,
                                );
                              }
                           

                            default:
                              log("Unhandled case for title: $title");
                          }
                        } else {
                          log("Title is null for the tapped card at index $index");
                        }
                      };
                    },
                  ),
                ),
              ),
              const Gap(70),
              Column(
                children: [
                  Row(
                    children: [
                      Text(
                        "Analytics/ Overview",
                        style: titleTextBold,
                      ),
                      const Spacer(),
                      SizedBox(
                        width: 200,
                        child: CustomDropdownFormField(
                          items: [
                            'all cities',
                            'mumbai',
                            'hyderabad',
                            'bangalore',
                            'pune'
                          ]
                              .map((item) => DropdownMenuItem(
                                  value: item,
                                  child: Text(item.capitalizeFirst.toString(),
                                      overflow: TextOverflow.ellipsis)))
                              .toList(),
                          value: selectedCity.value,
                          onChanged: (value) {
                            if (value != null) {
                              selectedCity.value = value;
                              updateGraphData();
                            }
                          },
                          textStyle: bodyTextRegular,
                          hintText: '',
                        ),
                      ),
                      const Gap(20),
                      Text(
                        "Last 7 days",
                        style: bodyTextMedium,
                      )
                    ],
                  ),
                  const Gap(10),
                  Obx(
                    () => Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                            child: LineChartSample2(city: selectedCity.value)),
                        const Gap(10),
                        Expanded(
                          child: AdminHomeBarChart(
                            city: selectedCity.value,
                          ),
                        ),
                        const Gap(10),
                        Column(
                          children: [
                            TotalCountCard(
                              title: 'Total events',
                              totalCount: homePageController
                                  .homePageGraphData
                                  .value
                                  .analyticsResult
                                  .totalEvents
                                  .totalEvents,
                              change: homePageController.homePageGraphData.value
                                  .analyticsResult.totalEvents.change
                                  .toInt(),
                              percentageChange: homePageController
                                  .homePageGraphData
                                  .value
                                  .analyticsResult
                                  .totalEvents
                                  .change,
                            ),
                            const SizedBox(height: 16),
                            TotalCountCard(
                              title: 'Total classes',
                              totalCount: homePageController
                                  .homePageGraphData
                                  .value
                                  .analyticsResult
                                  .totalClasses
                                  .totalClasses,
                              change: homePageController.homePageGraphData.value
                                  .analyticsResult.totalClasses.change
                                  .toInt(),
                              percentageChange: homePageController
                                  .homePageGraphData
                                  .value
                                  .analyticsResult
                                  .totalClasses
                                  .change,
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  columWidget(String title, Widget widget) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: heading2TextRegular,
        ),
        widget
      ],
    );
  }
}

class TotalCountCard extends StatelessWidget {
  final String title;
  final int totalCount;
  final int change;
  final double percentageChange;

  const TotalCountCard({
    super.key,
    required this.title,
    required this.totalCount,
    required this.change,
    required this.percentageChange,
  });

  @override
  Widget build(BuildContext context) {
    final bool isPositiveChange = change >= 0;
    final Color changeColor = isPositiveChange ? Colors.green : Colors.red;
    final IconData changeIcon =
        isPositiveChange ? Icons.arrow_upward : Icons.arrow_downward;

    return Container(
      width: 150,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(11),
          border: Border.all(color: AppColors.backcolor, width: 1.0)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'Rubic',
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '$totalCount',
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                fontFamily: 'Rubic',
                color: Colors.deepPurple[800],
              ),
            ),
            Row(
              children: [
                Icon(
                  changeIcon,
                  color: changeColor,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  '${isPositiveChange ? '+' : ''}$change (${percentageChange.toStringAsFixed(1)}%)',
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'Rubic',
                    fontWeight: FontWeight.w500,
                    color: changeColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
