import 'dart:async';
import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_business_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/common_filter.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/kyc_review.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/custom_pagination.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/event/widgets/option_container.dart';

class AdminBusinessPage extends StatefulWidget {
  final int initialIndex;
  final String type;

  const AdminBusinessPage({
    super.key,
    this.initialIndex = 0,
    this.type = 'events',
  });

  @override
  State<AdminBusinessPage> createState() => _AdminBusinessPageState();
}

class _AdminBusinessPageState extends State<AdminBusinessPage> {
  final AdminUserController adminUserController = Get.find<AdminUserController>();
  final AdminBusinessController adminBusinessController = Get.find<AdminBusinessController>();
  int sortColumnIndex = 0;
  bool sortAscending = false;
  int pageNo = 0;
  String filterBy = "";
  late final int pageCount;
  final int? pages = 1;
  Timer? _debounce;
  String searchQuery = "";
  DateTimeRange? selectedDateRange;
  String selectedCity = "";
  String selectedStatus = "";
  String selectedEventType = "";
  String selectedClassType = "";
  String selectedCategoryType = "";
  bool isEvent = true;
  bool isClasses = false;
  String type = "events";
  late int currentTabIndex;
  late String userPosition;
  List<String> allowedTabs = [];
  int currentPageBusiness = 0;
  int currentPageKyc = 0;
  int currentPageEvents = 0;
  int currentPageClasses = 0;
  int currentPageReports = 0;
  late TextEditingController _searchController;
  // int itemsPerPage = 10;
  // bool isSwitching = false;

  @override
  void initState() {
    super.initState();
    userPosition = adminUserController.adminDetailsModel.value.position;
    _setAllowedTabs();
    currentTabIndex = _getInitialTabIndex();
    type = widget.type;
    if (type == 'events') {
      isEvent = true;
      isClasses = false;
    } else if (type == 'classes') {
      isEvent = false;
      isClasses = true;
    }
    _loadData();
    _searchController = TextEditingController(text: searchQuery);
  }

  // @override
  // void initState() {
  //   super.initState();
  //   currentTabIndex = widget.initialIndex;
  //   type = widget.type;
  //   if (type == 'events') {
  //     isEvent = true;
  //     isClasses = false;
  //   } else if (type == 'classes') {
  //     isEvent = false;
  //     isClasses = true;
  //   }
  //   _loadData();
  //   _searchController = TextEditingController(text: searchQuery);
  // }

  void _setAllowedTabs() {
    switch (userPosition) {
      case "Super Admin":
        allowedTabs = [
          'All business',
          'KYC verification',
          'Events',
          'Classes',
          'Reports'
        ];
        break;
      case "KYC":
        allowedTabs = ['KYC verification'];
        break;
      case "Events":
        allowedTabs = ['Events'];
        break;
      case "Classes":
        allowedTabs = ['Classes'];
        break;
      case "Reports":
        allowedTabs = ['Reports'];
        break;
      case "Moderator":
      default:
        allowedTabs = [];
        break;
    }
  }

  int _getInitialTabIndex() {
    if (allowedTabs.isEmpty) return 0;
    if (widget.initialIndex < allowedTabs.length) {
      return widget.initialIndex;
    }
    return 0;
  }

  void resetFilterAndLoadData(int tabIndex) {
    setState(() {
      currentTabIndex = tabIndex;
      searchQuery = "";
      selectedDateRange = null;
      selectedCity = "";
      selectedStatus = "";
      selectedEventType = "";
      selectedClassType = "";
      selectedCategoryType = "";
      currentPageBusiness = 0;
      currentPageKyc = 0;
      currentPageEvents = 0;
      currentPageClasses = 0;
      currentPageReports = 0;
    });

    _loadData();
  }

  void clearFilterAndLoadData() {
    setState(() {
      searchQuery = "";
      selectedDateRange = null;
      selectedCity = "";
      selectedStatus = "";
      selectedEventType = "";
      selectedClassType = "";
      selectedCategoryType = "";
      currentPageBusiness = 0;
      currentPageKyc = 0;
      currentPageEvents = 0;
      currentPageClasses = 0;
      currentPageReports = 0;
    });

    _loadData();
  }

  @override
  void dispose() {
    adminBusinessController.dispose();
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _loadData() {
    String startDateString = selectedDateRange?.start.toIso8601String() ?? '';
    String endDateString = selectedDateRange?.end.toIso8601String() ?? '';

    switch (allowedTabs[currentTabIndex]) {
      case 'All business':
        adminBusinessController.getAllAdminBusinessList(
          search: searchQuery,
          startDate: startDateString,
          endDate: endDateString,
          city: selectedCity,
          page: currentPageBusiness,
        );
        break;
      case 'KYC verification':
        adminBusinessController.getAdminKycList(
          status: selectedStatus,
          startDate: startDateString,
          endDate: endDateString,
          page: currentPageKyc,
          search: searchQuery,
        );
        break;
      case 'Events':
        adminBusinessController.getAdminEventList(
          status: selectedStatus,
          startDate: startDateString,
          endDate: endDateString,
          search: searchQuery,
          page: currentPageEvents,
          eventType: selectedClassType,
        );
        break;
      case 'Classes':
        adminBusinessController.getAdminClassList(
          status: selectedStatus,
          startDate: startDateString,
          endDate: endDateString,
          search: searchQuery,
          page: currentPageClasses,
          classType: selectedClassType,
        );
        break;
      case 'Reports':
        adminBusinessController.getAdminBusinessReportsData(
          type: type,
          status: selectedStatus,
          startDate: startDateString,
          endDate: endDateString,
          search: searchQuery,
          page: currentPageReports,
        );
        break;
    }
  }

  void _onSearchChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      setState(() {
        searchQuery = value;
        currentPageBusiness = 0;
        currentPageKyc = 0;
        currentPageEvents = 0;
        currentPageClasses = 0;
        currentPageReports = 0;
        _loadData();
      });
    });
  }

  void _exportBusinessListData() async {
    String startDateString = selectedDateRange?.start.toIso8601String() ?? '';
    String endDateString = selectedDateRange?.end.toIso8601String() ?? '';
    adminBusinessController.getAllAdminBusinessList(
      search: searchQuery,
      startDate: startDateString,
      endDate: endDateString,
      city: selectedCity,
      page: currentPageBusiness,
      action: "export",
    );
  }

  void _exportAdminKycListData() async {
    String startDateString = selectedDateRange?.start.toIso8601String() ?? '';
    String endDateString = selectedDateRange?.end.toIso8601String() ?? '';

    adminBusinessController.getAdminKycList(
      status: selectedStatus,
      startDate: startDateString,
      endDate: endDateString,
      page: currentPageKyc,
      search: searchQuery,
      action: "export",
    );
  }

  void _exportEventListData() async {
    String startDateString = selectedDateRange?.start.toIso8601String() ?? '';
    String endDateString = selectedDateRange?.end.toIso8601String() ?? '';

    adminBusinessController.getAdminEventList(
      status: selectedStatus,
      startDate: startDateString,
      endDate: endDateString,
      search: searchQuery,
      page: currentPageEvents,
      eventType: selectedClassType,
      action: "export",
    );
  }

  void _exportClassListData() async {
    String startDateString = selectedDateRange?.start.toIso8601String() ?? '';
    String endDateString = selectedDateRange?.end.toIso8601String() ?? '';

    adminBusinessController.getAdminClassList(
      status: selectedStatus,
      startDate: startDateString,
      endDate: endDateString,
      search: searchQuery,
      page: currentPageClasses,
      classType: selectedClassType,
      action: "export",
    );
  }

  void _exportReportListData() async {
    String startDateString = selectedDateRange?.start.toIso8601String() ?? '';
    String endDateString = selectedDateRange?.end.toIso8601String() ?? '';

    adminBusinessController.getAdminBusinessReportsData(
      type: type,
      status: selectedStatus,
      startDate: startDateString,
      endDate: endDateString,
      search: searchQuery,
      page: currentPageReports,
      action: "export",
    );
  }

  @override
  Widget build(BuildContext context) {
    if (allowedTabs.isEmpty) {
      // Show error message for users without access
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.snackbar(
          'Error',
          "You don't have access to this page.",
          snackPosition: SnackPosition.TOP,
          backgroundColor: AppColors.errorRed,
          maxWidth: 300,
          colorText: AppColors.kwhite,
        );
        // Navigate back or to a default page
        Navigator.of(context).pop();
      });
      return const SizedBox.shrink();
    }

    return Scaffold(
      backgroundColor: Colors.white,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Business',
                style: heading2TextRegular,
              ),
              const Gap(10),
              DefaultTabController(
                length: allowedTabs.length,
                initialIndex: currentTabIndex,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TabBar(
                      isScrollable: true,
                      padding: EdgeInsets.zero,
                      indicatorColor: AppColors.kprimarycolor,
                      labelColor: AppColors.kprimarycolor,
                      unselectedLabelColor: AppColors.txtsecondary,
                      dividerColor: Colors.transparent,
                      unselectedLabelStyle: const TextStyle(
                          fontSize: 16.0,
                          fontWeight: FontWeight.w500,
                          color: AppColors.txtsecondary),
                      labelStyle: const TextStyle(
                          fontSize: 16.0,
                          fontWeight: FontWeight.w600,
                          color: AppColors.kprimarycolor),
                      overlayColor:
                          MaterialStateProperty.all(Colors.transparent),
                      onTap: (index) {
                        if (index != currentTabIndex) {
                          resetFilterAndLoadData(index);
                        }
                      },
                      tabs: allowedTabs.map((tab) => Tab(text: tab)).toList(),
                    ),
                    const Divider(
                      color: AppColors.backcolor,
                      height: 1.0,
                    ),
                    const SizedBox(height: 10),
                    SizedBox(
                      height: MediaQuery.of(context).size.height,
                      child: TabBarView(
                        physics: const NeverScrollableScrollPhysics(),
                        children: allowedTabs.map((tab) {
                          switch (tab) {
                            case 'All business':
                              return _allBusiness();
                            case 'KYC verification':
                              return _kycverification();
                            case 'Events':
                              return _events();
                            case 'Classes':
                              return _classes();
                            case 'Reports':
                              return _reports();
                            default:
                              return const SizedBox.shrink();
                          }
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _allBusiness() {
    return buildBusinessDataTable();
  }

  Widget _kycverification() {
    return buildKycVerificationDataTable();
  }

  Widget _events() {
    return buildEvetnsDataTable();
  }

  Widget _classes() {
    return buildClassDataTable();
  }

  Widget _reports() {
    return buildReportsTable();
  }

  Widget buildBusinessDataTable() {
    return Obx(
      () => adminBusinessController.isAdminBusinessListLoading.value
          ? const Center(child: CircularProgressIndicator.adaptive())
          : SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  FilterBar(
                    isTotal: true,
                    searchController: _searchController,
                    onClearFilter: clearFilterAndLoadData,
                    totalText:
                        "Total businesses ${adminBusinessController.businessListTotalModel.first.total}",
                    isAllCities: true,
                    onSearch: _onSearchChanged,
                    onStatusChanged: (value) {
                      setState(() {
                        selectedStatus = value ?? "";
                      });
                      _loadData();
                    },
                    onOnlineOfflineChanged: (value) {
                      setState(() {
                        selectedClassType = value ?? "";
                      });
                      _loadData();
                    },
                    onCategoryChanged: (value) {
                      setState(() {
                        selectedCategoryType = value;
                      });
                      _loadData();
                    },
                    onCitiesChanged: (city) {
                      if (kDebugMode) {
                        log('City changed to: $city');
                      }
                      setState(() {
                        selectedCity = city!;
                      });
                      _loadData();
                    },
                    onDateRangeChanged: (range) {
                      log('Date Range changed to: $range');
                      setState(() {
                        selectedDateRange = range;
                      });
                      _loadData();
                    },
                    currentSearchQuery: searchQuery,
                    currentDateRange: selectedDateRange,
                    currentCity: selectedCity,
                    currentStatus: selectedStatus,
                    currentOnlineOffline: selectedClassType,
                    currentCategory: selectedCategoryType,
                    onExportCSV: _exportBusinessListData,
                    statusItems: const ['All', 'Active', 'Inactive'],
                    onlineOfflineItems: const ['Online', 'Offline'],
                    categoryItems: const [
                      'Category 1',
                      'Category 2',
                      'Category 3'
                    ],
                    citiesItems: const [
                      "All Cities",
                      "Mumbai",
                      "Pune",
                      "Bangalore",
                      "Hyderabad"
                    ],
                  ),
                  const Gap(20),
                  adminBusinessController.isAdminBusinessListLoading.value
                      ? const Center(
                          child: CircularProgressIndicator.adaptive())
                      : adminBusinessController.adminBusinessList.isEmpty
                          ? Center(
                              child: Text("No data available",
                                  style: bodyTextSemiBold),
                            )
                          : SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: DataTable(
                                showCheckboxColumn: false,
                                headingTextStyle: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                                dataTextStyle: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                sortColumnIndex: sortColumnIndex,
                                sortAscending: sortAscending,
                                dataRowMaxHeight: 56,
                                border: TableBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  left: const BorderSide(
                                      color: AppColors.bordergrey),
                                  right: const BorderSide(
                                      color: AppColors.bordergrey),
                                  top: const BorderSide(
                                      color: AppColors.bordergrey),
                                  bottom: const BorderSide(
                                      color: AppColors.bordergrey),
                                  verticalInside: BorderSide.none,
                                ),
                                headingRowColor: MaterialStateColor.resolveWith(
                                    (states) => AppColors.klightwhite),
                                columns: [
                                  DataColumn(
                                    label: Text('Sr No', style: body2TextBold),
                                  ),
                                  // DataColumn(
                                  //   label: Text('View', style: body2TextBold),
                                  //   tooltip: 'View',
                                  // ),
                                  DataColumn(
                                    label: Text('Logo', style: body2TextBold),
                                  ),
                                  DataColumn(
                                    label: Text('Name', style: body2TextBold),
                                    tooltip: 'Business Name',
                                  ),
                                  DataColumn(
                                    label:
                                        Text('Joined on', style: body2TextBold),
                                    tooltip: 'Joined date',
                                  ),
                                  DataColumn(
                                    label: Text('Verification',
                                        style: body2TextBold),
                                    tooltip: 'Verification status',
                                  ),
                                  DataColumn(
                                    label:
                                        Text('Logged In', style: body2TextBold),
                                    tooltip: 'Business e-mail',
                                  ),
                                  DataColumn(
                                    label: Text('Business type',
                                        style: body2TextBold),
                                    tooltip: 'Business type',
                                  ),
                                  DataColumn(
                                    label: Text('City', style: body2TextBold),
                                    tooltip: 'Business city',
                                  ),
                                ],
                                rows: List<DataRow>.generate(
                                    adminBusinessController
                                        .adminBusinessList.length, (index) {
                                  final status = adminBusinessController
                                      .adminBusinessList[index].status;

                                  Color statusColor;
                                  switch (status.toLowerCase()) {
                                    case 'approved':
                                      statusColor = AppColors.kgreen;
                                      break;
                                    case 'rejected':
                                      statusColor = AppColors.errorRed;
                                      break;
                                    case 'unverified':
                                      statusColor = AppColors.kwarningbold;
                                      break;
                                    default:
                                      statusColor = AppColors.kprimarycolor;
                                  }

                                  return DataRow(
                                    onSelectChanged: (value) {
                                      locator<NavigationServices>().navigateTo(
                                          adminBusinessDetailsRoute,
                                          arguments: <String, int>{
                                            "businessByID":
                                                adminBusinessController
                                                    .adminBusinessList[index]
                                                    .businessId
                                          });
                                    },
                                    cells: [
                                      DataCell(
                                        Text(
                                          "${index + 1}",
                                        ),
                                      ),
                                      // DataCell(
                                      //   IconButton(
                                      //     icon: Icon(
                                      //       CupertinoIcons.eye_fill,
                                      //       color: AppColors.kprimarycolor
                                      //           .withOpacity(0.9),
                                      //     ),
                                      //     onPressed: () {
                                      //       log("adminBusinessDetailsRoute");
                                      //       locator<NavigationServices>()
                                      //           .navigateTo(
                                      //               adminBusinessDetailsRoute,
                                      //               arguments: <String, int>{
                                      //             "businessByID":
                                      //                 adminBusinessController
                                      //                     .adminBusinessList[
                                      //                         index]
                                      //                     .businessId
                                      //           });
                                      //     },
                                      //   ),
                                      // ),
                                      DataCell(ClipOval(
                                        child: CachedNetworkImage(
                                            imageUrl: adminBusinessController
                                                .adminBusinessList[index]
                                                .profilePictureUrl,
                                            height: 40.0,
                                            width: 40.0,
                                            fit: BoxFit.cover,
                                            errorWidget:
                                                (context, url, error) =>
                                                    SvgPicture.asset(
                                                        "assets/icons/logo.svg",
                                                        height: 40.0,
                                                        width: 40.0,
                                                        fit: BoxFit.cover),
                                            placeholder: (context, url) =>
                                                SvgPicture.asset(
                                                    "assets/icons/logo.svg",
                                                    height: 40.0,
                                                    width: 40.0,
                                                    fit: BoxFit.cover)),
                                      )),
                                      DataCell(Text(
                                        adminBusinessController
                                            .adminBusinessList[index]
                                            .businessName,
                                        style: bodyTextRegular,
                                      )),
                                      DataCell(Text(
                                        dateFormat(adminBusinessController
                                            .adminBusinessList[index].joinedOn),
                                        style: bodyTextRegular,
                                      )),
                                      DataCell(
                                        Text(
                                          status.capitalizeFirst.toString(),
                                          style: bodyTextRegular.copyWith(
                                              color: statusColor),
                                        ),
                                      ),
                                      DataCell(
                                        Text(
                                          // adminBusinessController
                                          //     .adminBusinessList[index].email,
                                          adminBusinessController
                                                  .adminBusinessList[index]
                                                  .loginat
                                                  .isEmpty
                                              ? "_"
                                              : adminBusinessController
                                                  .adminBusinessList[index]
                                                  .loginat,
                                          style: bodyTextRegular,
                                        ),
                                      ),
                                      DataCell(Text(
                                        adminBusinessController
                                                .adminBusinessList[index]
                                                .businessType
                                                .isEmpty
                                            ? "-"
                                            : adminBusinessController
                                                .adminBusinessList[index]
                                                .businessType,
                                        style: bodyTextRegular,
                                      )),
                                      DataCell(Text(
                                        adminBusinessController
                                                .adminBusinessList[index]
                                                .locationDetails
                                                .first
                                                .city
                                                .isEmpty
                                            ? "-"
                                            : adminBusinessController
                                                .adminBusinessList[index]
                                                .locationDetails
                                                .first
                                                .city,
                                        style: bodyTextRegular,
                                      )),
                                    ],
                                  );
                                }),
                              ),
                            ),
                  const Gap(15),
                  if (adminBusinessController
                          .businessListTotalModel.first.total >
                      0)
                    Row(
                      children: [
                        const Spacer(),
                        Expanded(
                          child: CustomPagination(
                            currentPage: currentPageBusiness,
                            totalItems: adminBusinessController
                                .businessListTotalModel.first.total,
                            itemsPerPage: 10,
                            onPageChanged: (int index) {
                              setState(() {
                                currentPageBusiness = index;
                              });
                              _loadData();
                            },
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
    );
  }

  Widget buildKycVerificationDataTable() {
    return Obx(() => adminBusinessController.isAdminkycListLoading.value
        ? const Center(child: CircularProgressIndicator.adaptive())
        : Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 32),
                child: FilterBar(
                  searchController: _searchController,
                  isAllStatus: true,
                  isTotal: true,
                  onClearFilter: clearFilterAndLoadData,
                  totalText:
                      "Total kyc ${adminBusinessController.businessListTotalModel.first.total}",
                  onSearch: _onSearchChanged,
                  onStatusChanged: (value) {
                    setState(() {
                      selectedStatus = value ?? "";
                    });
                    _loadData();
                  },
                  onOnlineOfflineChanged: (value) {
                    setState(() {
                      selectedClassType = value ?? "";
                    });
                    _loadData();
                  },
                  onCategoryChanged: (value) {
                    setState(() {
                      selectedCategoryType = value;
                    });
                    _loadData();
                  },
                  onCitiesChanged: (city) {
                    if (kDebugMode) {
                      log('City changed to: $city');
                    }
                    setState(() {
                      selectedCity = city!;
                    });
                    _loadData();
                  },
                  onDateRangeChanged: (range) {
                    log('Date Range changed to: $range');
                    setState(() {
                      selectedDateRange = range;
                    });
                    _loadData();
                  },
                  currentSearchQuery: searchQuery,
                  currentDateRange: selectedDateRange,
                  currentCity: selectedCity,
                  currentStatus: selectedStatus,
                  currentOnlineOffline: selectedClassType,
                  currentCategory: selectedCategoryType,
                  onExportCSV: _exportAdminKycListData,
                  statusItems: const [
                    'All',
                    'Requested',
                    'Approved',
                    "Rejected"
                  ],
                  onlineOfflineItems: const ['Online', 'Offline'],
                  categoryItems: const [
                    'Category 1',
                    'Category 2',
                    'Category 3'
                  ],
                  citiesItems: const [
                    "All Cities",
                    "Mumbai",
                    "Pune",
                    "Bangalore",
                    "Hyderabad"
                  ],
                ),
              ),
              const Gap(20),
              adminBusinessController.adminKycList.isEmpty
                  ? Center(
                      child: Text("No data available", style: bodyTextSemiBold),
                    )
                  : SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        showCheckboxColumn: false,
                        columnSpacing: 80,
                        headingTextStyle: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                        dataTextStyle: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        sortColumnIndex: sortColumnIndex,
                        sortAscending: sortAscending,
                        dataRowMaxHeight: 56,
                        border: TableBorder(
                          borderRadius: BorderRadius.circular(12),
                          left: const BorderSide(color: AppColors.bordergrey),
                          right: const BorderSide(color: AppColors.bordergrey),
                          top: const BorderSide(color: AppColors.bordergrey),
                          bottom: const BorderSide(color: AppColors.bordergrey),
                          verticalInside: BorderSide.none,
                        ),
                        headingRowColor: MaterialStateColor.resolveWith(
                            (states) => AppColors.klightwhite),
                        columns: [
                          DataColumn(
                            label: Text('Sr No', style: body2TextBold),
                          ),
                          // DataColumn(
                          //   label: Text('View', style: body2TextBold),
                          // ),
                          DataColumn(
                            label: Text('Logo', style: body2TextBold),
                          ),
                          DataColumn(
                            label: Text('Name', style: body2TextBold),
                          ),
                          DataColumn(
                            label: Text('Submitted on', style: body2TextBold),
                          ),
                          DataColumn(
                            label: Text('Status', style: body2TextBold),
                          ),
                          DataColumn(
                            label: Text('Reason/ notes', style: body2TextBold),
                          ),
                          DataColumn(
                            label: Text('Done By', style: body2TextBold),
                          ),
                        ],
                        rows: List<DataRow>.generate(
                          adminBusinessController.adminKycList.length,
                          (index) {
                            final status = adminBusinessController
                                .adminKycList[index].status;
                            Color statusColor;
                            switch (status.toLowerCase()) {
                              case 'approved':
                                statusColor = AppColors.kgreen;
                                break;
                              case 'rejected':
                                statusColor = AppColors.errorRed;
                                break;
                              case 'requested':
                                statusColor = AppColors.kwarningbold;
                                break;
                              default:
                                statusColor = Colors.black;
                            }

                            return DataRow(
                              onSelectChanged: (value) {
                                Get.dialog(
                                  KycReviewPanel(
                                    content: KycReviewPage(
                                        requestID: adminBusinessController
                                            .adminKycList[index].requestId),
                                  ),
                                  barrierDismissible: true,
                                );
                              },
                              cells: [
                                DataCell(
                                  Text(
                                    "${index + 1}",
                                  ),
                                ),
                                // DataCell(
                                //   IconButton(
                                //     icon: Icon(
                                //       CupertinoIcons.eye_fill,
                                //       color: AppColors.kprimarycolor
                                //           .withOpacity(0.9),
                                //     ),
                                //     onPressed: () {
                                //       Get.dialog(
                                //         KycReviewPanel(
                                //           content: KycReviewPage(
                                //               requestID: adminBusinessController
                                //                   .adminKycList[index]
                                //                   .requestId),
                                //         ),
                                //         barrierDismissible: true,
                                //       );
                                //     },
                                //   ),
                                // ),
                                DataCell(ClipOval(
                                  child: CachedNetworkImage(
                                      imageUrl: adminBusinessController
                                          .adminKycList[index]
                                          .profilePictureUrl,
                                      height: 40.0,
                                      width: 40.0,
                                      fit: BoxFit.cover,
                                      errorWidget: (context, url, error) =>
                                          SvgPicture.asset(
                                              "assets/icons/logo.svg",
                                              height: 40.0,
                                              width: 40.0,
                                              fit: BoxFit.cover),
                                      placeholder: (context, url) =>
                                          SvgPicture.asset(
                                              "assets/icons/logo.svg",
                                              height: 40.0,
                                              width: 40.0,
                                              fit: BoxFit.cover)),
                                )),
                                DataCell(
                                  Text(
                                    adminBusinessController
                                        .adminKycList[index].businessName,
                                    style: bodyTextRegular,
                                  ),
                                ),
                                DataCell(
                                  Text(
                                    dateFormat(adminBusinessController
                                        .adminKycList[index].submittedOn),
                                    // "24 May 2024",
                                    style: bodyTextRegular,
                                  ),
                                ),
                                DataCell(
                                  Text(
                                    status.capitalizeFirst.toString(),
                                    style: bodyTextRegular.copyWith(
                                        color: statusColor),
                                  ),
                                ),
                                DataCell(
                                  Text(
                                      adminBusinessController
                                              .adminKycList[index]
                                              .reason
                                              .isEmpty
                                          ? "-"
                                          : adminBusinessController
                                              .adminKycList[index].reason,
                                      style: bodyTextRegular,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis),
                                ),
                                DataCell(
                                  Text(
                                    adminBusinessController
                                            .adminKycList[index].doneBy.isEmpty
                                        ? "-"
                                        : adminBusinessController
                                            .adminKycList[index].doneBy,
                                    style: bodyTextRegular,
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ),
              const Gap(15),
              if (adminBusinessController.businessListTotalModel.first.total >
                  0)
                Align(
                  alignment: Alignment.centerRight,
                  child: SizedBox(
                      width: Get.width * .3,
                      child: CustomPagination(
                        currentPage: currentPageKyc,
                        totalItems: adminBusinessController
                            .businessListTotalModel.first.total,
                        itemsPerPage: 10,
                        onPageChanged: (int index) {
                          setState(() {
                            currentPageKyc = index;
                          });
                          _loadData();
                        },
                      )),
                ),
            ],
          ));
  }

  Widget buildEvetnsDataTable() {
    return Obx(
      () => adminBusinessController.isAdminEventListLoading.value
          ? const Center(child: CircularProgressIndicator.adaptive())
          : ScrollConfiguration(
              behavior:
                  ScrollConfiguration.of(context).copyWith(scrollbars: false),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    FilterBar(
                      isAllStatus: true,
                      searchController: _searchController,
                      onClearFilter: clearFilterAndLoadData,
                      isTotal: true,
                      totalText:
                          "Total events ${adminBusinessController.businessListTotalModel.first.total}",
                      isClassType: true,
                      onSearch: _onSearchChanged,
                      onStatusChanged: (value) {
                        setState(() {
                          selectedStatus = value ?? "";
                        });
                        _loadData();
                      },
                      onOnlineOfflineChanged: (value) {
                        setState(() {
                          selectedClassType = value ?? "";
                        });
                        _loadData();
                      },
                      onCategoryChanged: (value) {
                        setState(() {
                          selectedCategoryType = value;
                        });
                        _loadData();
                      },
                      onCitiesChanged: (city) {
                        if (kDebugMode) {
                          log('City changed to: $city');
                        }
                        setState(() {
                          selectedCity = city!;
                        });
                        _loadData();
                      },
                      onDateRangeChanged: (range) {
                        log('Date Range changed to: $range');
                        setState(() {
                          selectedDateRange = range;
                        });
                        _loadData();
                      },
                      currentSearchQuery: searchQuery,
                      currentDateRange: selectedDateRange,
                      currentCity: selectedCity,
                      currentStatus: selectedStatus,
                      currentOnlineOffline: selectedClassType,
                      currentCategory: selectedCategoryType,
                      onExportCSV: _exportEventListData,
                      statusItems: const [
                        'All',
                        'Inreview',
                        'Rejected',
                        "Approved",
                        "Draft"
                      ],
                      onlineOfflineItems: const ['Online', 'Offline'],
                      categoryItems: const [
                        'Category 1',
                        'Category 2',
                        'Category 3'
                      ],
                      citiesItems: const [
                        "All Cities",
                        "Mumbai",
                        "Pune",
                        "Bangalore",
                        "Hyderabad"
                      ],
                    ),
                    const Gap(20),
                    if (adminBusinessController.adminEventList.isEmpty)
                      Center(
                        child:
                            Text("No data available", style: bodyTextSemiBold),
                      )
                    else
                      ScrollConfiguration(
                        behavior: ScrollConfiguration.of(context)
                            .copyWith(scrollbars: false),
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: DataTable(
                            columnSpacing: 80,
                            showCheckboxColumn: false,
                            headingTextStyle: const TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                            dataTextStyle: const TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            sortColumnIndex: sortColumnIndex,
                            sortAscending: sortAscending,
                            dataRowMaxHeight: 56,
                            border: TableBorder(
                              borderRadius: BorderRadius.circular(12),
                              left:
                                  const BorderSide(color: AppColors.bordergrey),
                              right:
                                  const BorderSide(color: AppColors.bordergrey),
                              top:
                                  const BorderSide(color: AppColors.bordergrey),
                              bottom:
                                  const BorderSide(color: AppColors.bordergrey),
                              verticalInside: BorderSide.none,
                            ),
                            headingRowColor: MaterialStateColor.resolveWith(
                                (states) => AppColors.klightwhite),
                            columns: [
                              DataColumn(
                                label: Text('Sr No', style: body2TextBold),
                              ),
                              // DataColumn(
                              //   label: Text('View', style: body2TextBold),
                              //   // tooltip: 'Done By',
                              // ),
                              DataColumn(
                                label: Text('Logo', style: body2TextBold),
                              ),
                              DataColumn(
                                label:
                                    Text('Event title', style: body2TextBold),
                                // tooltip: 'Business Name',
                              ),
                              DataColumn(
                                label:
                                    Text('Submitted on', style: body2TextBold),
                                // tooltip: 'Submitted date',]
                              ),
                              DataColumn(
                                label: Text('Status', style: body2TextBold),
                                // tooltip: 'Submittion status',
                              ),
                              DataColumn(
                                label: Text('Event start date',
                                    style: body2TextBold),
                                // tooltip: 'Actions',
                              ),
                              DataColumn(
                                label: Text('Organiser', style: body2TextBold),
                                // tooltip: 'Reasons type',
                              ),
                              DataColumn(
                                label: Text('Event type', style: body2TextBold),
                                // tooltip: 'Done By',
                              ),
                              DataColumn(
                                label: Text('City', style: body2TextBold),
                                // tooltip: 'Done By',
                              ),
                              DataColumn(
                                label: Text('Done By', style: body2TextBold),
                                // tooltip: 'Done By',
                              ),
                            ],
                            rows: List<DataRow>.generate(
                              adminBusinessController.adminEventList.length,
                              (index) {
                                final status = adminBusinessController
                                    .adminEventList[index].status;

                                Color statusColor;
                                switch (status.toLowerCase()) {
                                  case 'approved':
                                    statusColor = AppColors.kgreen;
                                    break;
                                  case 'rejected':
                                    statusColor = AppColors.errorRed;
                                    break;
                                  case 'inreview':
                                    statusColor = AppColors.kwarningbold;
                                    break;
                                  default:
                                    statusColor = Colors.deepPurpleAccent;
                                }

                                return DataRow(
                                  onSelectChanged: (value) {
                                    locator<NavigationServices>().navigateTo(
                                        adminEventDetailsPage,
                                        arguments: <String, int>{
                                          'requestID': adminBusinessController
                                              .adminEventList[index].requestID
                                        });
                                  },
                                  cells: [
                                    DataCell(
                                      Text(
                                        "${index + 1}",
                                      ),
                                    ),
                                    // DataCell(
                                    //   IconButton(
                                    //     icon: Icon(
                                    //       CupertinoIcons.eye_fill,
                                    //       color: AppColors.kprimarycolor
                                    //           .withOpacity(0.9),
                                    //     ),
                                    //     onPressed: () {
                                    //       locator<NavigationServices>()
                                    //           .navigateTo(adminEventDetailsPage,
                                    //               arguments: <String, int>{
                                    //             'requestID':
                                    //                 adminBusinessController
                                    //                     .adminEventList[index]
                                    //                     .requestID
                                    //           });
                                    //     },
                                    //   ),
                                    // ),
                                    DataCell(
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(12),
                                        child: CachedNetworkImage(
                                            imageUrl: adminBusinessController
                                                .adminEventList[index]
                                                .bannerUrl,
                                            height: 40.0,
                                            width: 40.0,
                                            fit: BoxFit.cover,
                                            errorWidget: (context, url,
                                                    error) =>
                                                Image.asset(
                                                    "assets/images/event_banner_empty.png",
                                                    height: 40.0,
                                                    width: 40.0,
                                                    fit: BoxFit.cover),
                                            placeholder: (context, url) =>
                                                Image.asset(
                                                    "assets/images/event_banner_empty.png",
                                                    height: 40.0,
                                                    width: 40.0,
                                                    fit: BoxFit.cover)),
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                            .adminEventList[index].title,
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        dateFormat(adminBusinessController
                                            .adminEventList[index].submittedOn),
                                        // "24 May 2024",
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        status.capitalizeFirst.toString(),
                                        style: bodyTextRegular.copyWith(
                                            color: statusColor),
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        dateFormat(adminBusinessController
                                            .adminEventList[index].startDate),
                                        // "24 May 2024",
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                            .adminEventList[index].businessName,
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                            .adminEventList[index]
                                            .eventType
                                            .capitalizeFirst
                                            .toString()
                                            .replaceAll("Offline + online",
                                                "Offline + Online"),
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                            .adminEventList[index].city,
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                                .adminEventList[index]
                                                .doneBy
                                                .isEmpty
                                            ? "-"
                                            : adminBusinessController
                                                .adminEventList[index].doneBy,
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    const Gap(15),
                    if (adminBusinessController
                            .businessListTotalModel.first.total >
                        0)
                      Row(
                        children: [
                          const Spacer(),
                          Expanded(
                            child: CustomPagination(
                              currentPage: currentPageEvents,
                              totalItems: adminBusinessController
                                  .businessListTotalModel.first.total,
                              itemsPerPage: 10,
                              onPageChanged: (int index) {
                                setState(() {
                                  currentPageEvents = index;
                                });
                                _loadData();
                              },
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget buildClassDataTable() {
    return Obx(
      () => adminBusinessController.isAdminClassListLoading.value
          ? const Center(child: CircularProgressIndicator.adaptive())
          : ScrollConfiguration(
              behavior:
                  ScrollConfiguration.of(context).copyWith(scrollbars: false),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    FilterBar(
                      isAllStatus: true,
                      searchController: _searchController,
                      isTotal: true,
                      onClearFilter: clearFilterAndLoadData,
                      totalText:
                          "Total classes ${adminBusinessController.businessListTotalModel.first.total}",
                      isClassType: true,
                      onSearch: _onSearchChanged,
                      onStatusChanged: (value) {
                        setState(() {
                          selectedStatus = value ?? "";
                        });
                        _loadData();
                      },
                      onOnlineOfflineChanged: (value) {
                        setState(() {
                          selectedClassType = value ?? "";
                        });
                        _loadData();
                      },
                      onCategoryChanged: (value) {
                        setState(() {
                          selectedCategoryType = value;
                        });
                        _loadData();
                      },
                      onCitiesChanged: (city) {
                        if (kDebugMode) {
                          log('City changed to: $city');
                        }
                        setState(() {
                          selectedCity = city!;
                        });
                        _loadData();
                      },
                      onDateRangeChanged: (range) {
                        log('Date Range changed to: $range');
                        setState(() {
                          selectedDateRange = range;
                        });
                        _loadData();
                      },
                      currentSearchQuery: searchQuery,
                      currentDateRange: selectedDateRange,
                      currentCity: selectedCity,
                      currentStatus: selectedStatus,
                      currentOnlineOffline: selectedClassType,
                      currentCategory: selectedCategoryType,
                      onExportCSV: _exportClassListData,
                      statusItems: const [
                        'All',
                        'Inreview',
                        'Rejected',
                        "Approved",
                        "Draft"
                      ],
                      onlineOfflineItems: const ['Online', 'Offline'],
                      categoryItems: const [
                        'Category 1',
                        'Category 2',
                        'Category 3'
                      ],
                      citiesItems: const [
                        "All Cities",
                        "Mumbai",
                        "Pune",
                        "Bangalore",
                        "Hyderabad"
                      ],
                    ),
                    const Gap(20),
                    if (adminBusinessController.adminClassList.isEmpty)
                      Center(
                        child:
                            Text("No data available", style: bodyTextSemiBold),
                      )
                    else
                      ScrollConfiguration(
                        behavior: ScrollConfiguration.of(context)
                            .copyWith(scrollbars: false),
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: DataTable(
                            columnSpacing: 80,
                            showCheckboxColumn: false,
                            headingTextStyle: const TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                            dataTextStyle: const TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            sortColumnIndex: sortColumnIndex,
                            sortAscending: sortAscending,
                            dataRowMaxHeight: 56,
                            border: TableBorder(
                              borderRadius: BorderRadius.circular(12),
                              left:
                                  const BorderSide(color: AppColors.bordergrey),
                              right:
                                  const BorderSide(color: AppColors.bordergrey),
                              top:
                                  const BorderSide(color: AppColors.bordergrey),
                              bottom:
                                  const BorderSide(color: AppColors.bordergrey),
                              verticalInside: BorderSide.none,
                            ),
                            headingRowColor: MaterialStateColor.resolveWith(
                                (states) => AppColors.klightwhite),
                            columns: [
                              DataColumn(
                                label: Text('Sr No', style: body2TextBold),
                              ),
                              // DataColumn(
                              //   label: Text('View', style: body2TextBold),
                              //   // tooltip: 'Done By',
                              // ),
                              DataColumn(
                                label: Text('Image', style: body2TextBold),
                              ),
                              DataColumn(
                                label:
                                    Text('Class title', style: body2TextBold),
                                // tooltip: 'Business Name',
                              ),
                              DataColumn(
                                label:
                                    Text('Submitted on', style: body2TextBold),
                                // tooltip: 'Submitted date',]
                              ),
                              DataColumn(
                                label: Text('Status', style: body2TextBold),
                                // tooltip: 'Submittion status',
                              ),
                              DataColumn(
                                label: Text('Organiser', style: body2TextBold),
                                // tooltip: 'Reasons type',
                              ),
                              DataColumn(
                                label: Text('Category', style: body2TextBold),
                                // tooltip: 'Done By',
                              ),
                              DataColumn(
                                label: Text('Class Type', style: body2TextBold),
                                // tooltip: 'Done By',
                              ),
                              DataColumn(
                                label: Text('City', style: body2TextBold),
                                // tooltip: 'Done By',
                              ),
                              DataColumn(
                                label: Text('Done By', style: body2TextBold),
                                // tooltip: 'Done By',
                              ),
                            ],
                            rows: List<DataRow>.generate(
                              adminBusinessController.adminClassList.length,
                              (index) {
                                final status = adminBusinessController
                                    .adminClassList[index].status;

                                Color statusColor;
                                switch (status.toLowerCase()) {
                                  case 'approved':
                                    statusColor = AppColors.kgreen;
                                    break;
                                  case 'rejected':
                                    statusColor = AppColors.errorRed;
                                    break;
                                  case 'inreview':
                                    statusColor = AppColors.kwarningbold;
                                    break;
                                  default:
                                    statusColor = Colors.deepPurpleAccent;
                                }

                                return DataRow(
                                  onSelectChanged: (value) {
                                    locator<NavigationServices>().navigateTo(
                                        adminClassDetailsPage,
                                        arguments: <String, int>{
                                          'requestID': adminBusinessController
                                              .adminClassList[index].requestID
                                        });
                                  },
                                  cells: [
                                    DataCell(
                                      Text(
                                        "${index + 1}",
                                      ),
                                    ),
                                    // DataCell(
                                    //   IconButton(
                                    //     icon: Icon(
                                    //       CupertinoIcons.eye_fill,
                                    //       color: AppColors.kprimarycolor
                                    //           .withOpacity(0.9),
                                    //     ),
                                    //     onPressed: () {
                                    //       locator<NavigationServices>()
                                    //           .navigateTo(adminClassDetailsPage,
                                    //               arguments: <String, int>{
                                    //             'requestID':
                                    //                 adminBusinessController
                                    //                     .adminClassList[index]
                                    //                     .requestID
                                    //           });
                                    //     },
                                    //   ),
                                    // ),
                                    DataCell(ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: CachedNetworkImage(
                                          imageUrl: adminBusinessController
                                              .adminClassList[index].bannerUrl,
                                          height: 40.0,
                                          width: 40.0,
                                          fit: BoxFit.cover,
                                          errorWidget: (context, url, error) =>
                                              Image.asset(
                                                  "assets/images/class_banner_empty.png",
                                                  height: 40.0,
                                                  width: 40.0,
                                                  fit: BoxFit.cover),
                                          placeholder: (context, url) =>
                                              Image.asset(
                                                  "assets/images/class_banner_empty.png",
                                                  height: 40.0,
                                                  width: 40.0,
                                                  fit: BoxFit.cover)),
                                    )),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                            .adminClassList[index].title,
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        dateFormat(adminBusinessController
                                            .adminClassList[index].submittedOn),
                                        // "24 May 2024",
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        status.capitalizeFirst.toString(),
                                        style: bodyTextRegular.copyWith(
                                            color: statusColor),
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                            .adminClassList[index].businessName,
                                        // "24 May 2024",
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                            .adminClassList[index].category,
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                            .adminClassList[index]
                                            .classType
                                            .capitalizeFirst
                                            .toString()
                                            .replaceAll("Offline + online",
                                                "Offline + Online"),
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                                .adminClassList[index]
                                                .city
                                                .isEmpty
                                            ? "-"
                                            : adminBusinessController
                                                .adminClassList[index].city,
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        adminBusinessController
                                                .adminClassList[index]
                                                .doneBy
                                                .isEmpty
                                            ? "-"
                                            : adminBusinessController
                                                .adminClassList[index].doneBy,
                                        style: bodyTextRegular,
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    const Gap(15),
                    if (adminBusinessController
                            .businessListTotalModel.first.total >
                        0)
                      Row(
                        children: [
                          const Spacer(),
                          Expanded(
                            child: CustomPagination(
                              currentPage: currentPageClasses,
                              totalItems: adminBusinessController
                                  .businessListTotalModel.first.total,
                              itemsPerPage: 10,
                              onPageChanged: (int index) {
                                setState(() {
                                  currentPageClasses = index;
                                });
                                _loadData();
                              },
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget buildReportsTable() {
    return Obx(
      () => adminBusinessController.isAdminReportListLoading.value
          ? const Center(
              child: CircularProgressIndicator.adaptive(),
            )
          : Column(
              children: [
                Row(
                  children: [
                    Container(
                      height: 48,
                      width: 300,
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 4),
                      decoration: BoxDecoration(
                          color: AppColors.scaffoldColor,
                          borderRadius: BorderRadius.circular(50)),
                      child: Row(
                        children: [
                          OptionContainer(
                            text: 'Events',
                            isSelected: isEvent,
                            onTap: () {
                              if (!isEvent) {
                                setState(() {
                                  isEvent = true;
                                  isClasses = false;
                                  type = "events";
                                  adminBusinessController
                                      .adminBusinessReportList
                                      .clear();
                                  adminBusinessController
                                      .isAdminReportListLoading.value = true;
                                });
                                adminBusinessController
                                    .getAdminBusinessReportsData(
                                  type: type,
                                  page: currentPageReports,
                                );
                              }
                            },
                          ),
                          OptionContainer(
                            text: 'classes',
                            isSelected: isClasses,
                            onTap: () {
                              if (!isClasses) {
                                setState(() {
                                  isEvent = false;
                                  isClasses = true;
                                  type = "classes";
                                  currentPageReports = 0;
                                  adminBusinessController
                                      .adminBusinessReportList
                                      .clear();
                                  adminBusinessController
                                      .isAdminReportListLoading.value = true;
                                });
                                adminBusinessController
                                    .getAdminBusinessReportsData(
                                        type: type, page: currentPageReports);
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                    const Gap(20),
                    Expanded(
                      child: FilterBar(
                        isAllStatus: true,
                        isTotal: false,
                        searchController: _searchController,
                        onClearFilter: clearFilterAndLoadData,
                        totalText: isEvent
                            ? "Total reported events ${adminBusinessController.businessListTotalModel.first.total}"
                            : "Total reported classes ${adminBusinessController.businessListTotalModel.first.total}",
                        onSearch: _onSearchChanged,
                        onCategoryChanged: (value) {
                          setState(() {
                            selectedCategoryType = value;
                          });
                          _loadData();
                        },
                        onCitiesChanged: (city) {
                          if (kDebugMode) {
                            log('City changed to: $city');
                          }
                          setState(() {
                            selectedCity = city!;
                          });
                          _loadData();
                        },
                        onDateRangeChanged: (range) {
                          log('Date Range changed to: $range');
                          setState(() {
                            selectedDateRange = range;
                            currentPageReports = 0;
                          });
                          _loadData();
                        },
                        onStatusChanged: (value) {
                          setState(() {
                            selectedStatus = value!;
                            currentPageReports = 0;
                          });
                          _loadData();
                        },
                        onOnlineOfflineChanged: (value) {
                          setState(() {
                            selectedClassType = value ?? "";
                          });
                          _loadData();
                        },
                        onExportCSV: _exportReportListData,
                        currentSearchQuery: searchQuery,
                        currentDateRange: selectedDateRange,
                        currentCity: selectedCity,
                        currentStatus: selectedStatus,
                        currentOnlineOffline: selectedClassType,
                        currentCategory: selectedCategoryType,
                        statusItems: const [
                          'All',
                          'Inreview',
                          'Approved',
                          "Rejected"
                        ],
                        onlineOfflineItems: const ['Online', 'Offline'],
                        categoryItems: const [
                          'Category 1',
                          'Category 2',
                          'Category 3'
                        ],
                        citiesItems: const [
                          "All Cities",
                          "Mumbai",
                          "Pune",
                          "Bangalore",
                          "Hyderabad"
                        ],
                      ),
                    )
                  ],
                ),
                const Gap(20),
                adminBusinessController.isAdminReportListLoading.value
                    ? const Center(child: CircularProgressIndicator.adaptive())
                    : adminBusinessController.adminBusinessReportList.isEmpty
                        ? Center(
                            child: Text("No data available",
                                style: bodyTextSemiBold),
                          )
                        : ScrollConfiguration(
                            behavior: ScrollConfiguration.of(context)
                                .copyWith(scrollbars: false),
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: DataTable(
                                showCheckboxColumn: false,
                                columnSpacing: isEvent ? 80 : 90,
                                headingTextStyle: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                                dataTextStyle: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                sortColumnIndex: sortColumnIndex,
                                sortAscending: sortAscending,
                                dataRowMaxHeight: 56,
                                border: TableBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  left: const BorderSide(
                                      color: AppColors.bordergrey),
                                  right: const BorderSide(
                                      color: AppColors.bordergrey),
                                  top: const BorderSide(
                                      color: AppColors.bordergrey),
                                  bottom: const BorderSide(
                                      color: AppColors.bordergrey),
                                  verticalInside: BorderSide.none,
                                ),
                                headingRowColor: MaterialStateColor.resolveWith(
                                    (states) => AppColors.klightwhite),
                                columns: [
                                  DataColumn(
                                    label: Text('Sr No', style: body2TextBold),
                                  ),
                                  // DataColumn(
                                  //   label: Text('View', style: body2TextBold),
                                  // ),
                                  DataColumn(
                                    label: Text(
                                        isEvent ? 'Event name' : 'Class name',
                                        style: body2TextBold),
                                  ),
                                  DataColumn(
                                    label:
                                        Text('Organiser', style: body2TextBold),
                                  ),
                                  DataColumn(
                                    label: Text('Reported by',
                                        style: body2TextBold),
                                  ),
                                  DataColumn(
                                    label: Text('Status', style: body2TextBold),
                                  ),
                                  DataColumn(
                                    label:
                                        Text('Done By', style: body2TextBold),
                                  ),
                                ],
                                rows: List<DataRow>.generate(
                                  adminBusinessController
                                      .adminBusinessReportList.length,
                                  (index) => DataRow(
                                    onSelectChanged: (value) {
                                      if (isEvent) {
                                        locator<NavigationServices>()
                                            .navigateTo(
                                          adminReportEventDetails,
                                          arguments: <String, int>{
                                            'eventID': adminBusinessController
                                                .adminBusinessReportList[index]
                                                .eventId
                                          },
                                        );
                                      } else {
                                        locator<NavigationServices>()
                                            .navigateTo(
                                          adminReportClassDetailsPage,
                                          arguments: <String, int>{
                                            'classID': adminBusinessController
                                                .adminBusinessReportList[index]
                                                .classId
                                          },
                                        );
                                      }
                                    },
                                    cells: [
                                      DataCell(
                                        Text("${index + 1}"),
                                      ),
                                      // DataCell(
                                      //   IconButton(
                                      //     icon: Icon(CupertinoIcons.eye_fill,
                                      //         color: AppColors.kprimarycolor
                                      //             .withOpacity(0.9)),
                                      //     onPressed: isEvent
                                      //         ? () {
                                      //             locator<NavigationServices>()
                                      //                 .navigateTo(
                                      //                     adminReportEventDetails,
                                      //                     arguments: <String, int>{
                                      //                   'eventID':
                                      //                       adminBusinessController
                                      //                           .adminBusinessReportList[
                                      //                               index]
                                      //                           .eventId
                                      //                 });
                                      //           }
                                      //         : () {
                                      //             locator<NavigationServices>()
                                      //                 .navigateTo(
                                      //                     adminReportClassDetailsPage,
                                      //                     arguments: <String, int>{
                                      //                   'classID':
                                      //                       adminBusinessController
                                      //                           .adminBusinessReportList[
                                      //                               index]
                                      //                           .classId
                                      //                 });
                                      //             _loadData();
                                      //           },
                                      //   ),
                                      // ),
                                      DataCell(
                                        Text(
                                          isEvent
                                              ? adminBusinessController
                                                  .adminBusinessReportList[
                                                      index]
                                                  .eTitle
                                              : adminBusinessController
                                                  .adminBusinessReportList[
                                                      index]
                                                  .cTitle,
                                          style: bodyTextRegular,
                                        ),
                                      ),
                                      DataCell(
                                        Text(
                                          isEvent
                                              ? adminBusinessController
                                                  .adminBusinessReportList[
                                                      index]
                                                  .eBusinessName
                                              : adminBusinessController
                                                  .adminBusinessReportList[
                                                      index]
                                                  .cBusinessName,
                                          style: bodyTextRegular,
                                        ),
                                      ),
                                      DataCell(
                                        Text(
                                          isEvent
                                              ? adminBusinessController
                                                      .adminBusinessReportList[
                                                          index]
                                                      .eReportedBy
                                                      .isNotEmpty
                                                  ? adminBusinessController
                                                      .adminBusinessReportList[
                                                          index]
                                                      .eReportedBy
                                                      .first
                                                      .firstName
                                                  : '-'
                                              : adminBusinessController
                                                      .adminBusinessReportList[
                                                          index]
                                                      .cReportedBy
                                                      .isNotEmpty
                                                  ? adminBusinessController
                                                      .adminBusinessReportList[
                                                          index]
                                                      .cReportedBy
                                                      .first
                                                      .firstName
                                                  : '-',
                                          style: bodyTextRegular,
                                        ),
                                      ),
                                      DataCell(
                                        Text(
                                          isEvent
                                              ? adminBusinessController
                                                  .adminBusinessReportList[
                                                      index]
                                                  .eStatus
                                                  .capitalizeFirst
                                                  .toString()
                                              : adminBusinessController
                                                  .adminBusinessReportList[
                                                      index]
                                                  .cStatus
                                                  .capitalizeFirst
                                                  .toString(),
                                          style: bodyTextRegular.copyWith(
                                              color: (isEvent
                                                          ? adminBusinessController
                                                              .adminBusinessReportList[
                                                                  index]
                                                              .eStatus
                                                          : adminBusinessController
                                                              .adminBusinessReportList[
                                                                  index]
                                                              .cStatus) ==
                                                      "rejected"
                                                  ? AppColors.errorRed
                                                  : (isEvent
                                                              ? adminBusinessController
                                                                  .adminBusinessReportList[
                                                                      index]
                                                                  .eStatus
                                                              : adminBusinessController
                                                                  .adminBusinessReportList[
                                                                      index]
                                                                  .cStatus) ==
                                                          "inreview"
                                                      ? AppColors.kwarningbold
                                                      : AppColors.kgreen),
                                        ),
                                      ),
                                      DataCell(
                                        Text(
                                          isEvent
                                              ? adminBusinessController
                                                      .adminBusinessReportList[
                                                          index]
                                                      .eDoneBy
                                                      .isEmpty
                                                  ? "-"
                                                  : adminBusinessController
                                                      .adminBusinessReportList[
                                                          index]
                                                      .eDoneBy
                                              : adminBusinessController
                                                      .adminBusinessReportList[
                                                          index]
                                                      .cDoneBy
                                                      .isEmpty
                                                  ? "-"
                                                  : adminBusinessController
                                                      .adminBusinessReportList[
                                                          index]
                                                      .cDoneBy,
                                          style: bodyTextRegular,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                const Gap(15),
                if (adminBusinessController.businessListTotalModel.first.total >
                    0)
                  Align(
                    alignment: Alignment.centerRight,
                    child: CustomPagination(
                      currentPage: currentPageReports,
                      totalItems: adminBusinessController
                          .businessListTotalModel.first.total,
                      itemsPerPage: 10,
                      onPageChanged: (int index) {
                        setState(() {
                          currentPageReports = index;
                        });
                        _loadData();
                      },
                    ),
                  ),
              ],
            ),
    );
  }
}
