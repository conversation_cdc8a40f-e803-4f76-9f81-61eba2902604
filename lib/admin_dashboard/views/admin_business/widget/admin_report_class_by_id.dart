import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_business_controller.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/view/classes/widget/class_details.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';

class AdminReportClassDetailsPage extends StatefulWidget {
  const AdminReportClassDetailsPage({super.key, required this.arguments});
  final Map<String, int> arguments;

  @override
  State<AdminReportClassDetailsPage> createState() =>
      _AdminReportClassDetailsPageState();
}

class _AdminReportClassDetailsPageState
    extends State<AdminReportClassDetailsPage> {
  int id = 0;
  int sortColumnIndex = 0;
  bool sortAscending = false;
  RxBool isApproveReject = true.obs;
  RxBool isRejectReport = false.obs;
  String selectedRejectReason = "";
  final _reasonController = TextEditingController().obs;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final ClassController classVM = Get.find<ClassController>();
  final AdminBusinessController adminBusinessController = Get.find<AdminBusinessController>();
  List<String> rejectReaons = [
    'Profanity',
    'Inappropriate content and imagery.',
    'Contact details are valid.',
    "Contact number should not be in description"
  ];

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      setState(() {
        id = widget.arguments['classID']!;
      });
      adminBusinessController.getAdminReportClassDetailsData(id);
    });
  }

  @override
  void dispose() {
    adminBusinessController.clearAdminReportClassData();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.kwhite,
        body: ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                width: Get.width * .7,
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                  border: Border.all(
                    color: AppColors.kgrey,
                  ),
                ),
                child: Obx(
                  () => adminBusinessController
                          .isadminClassReportByIDLoading.value
                      ? const ClassDetailsShimmer()
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16.0, vertical: 0.0),
                              child: Row(
                                children: [
                                  InkWell(
                                    onTap: () {
                                      locator<NavigationServices>().goBack();
                                    },
                                    child: SvgPicture.asset(
                                        "assets/svg/arrow-left.svg"),
                                  ),
                                  const Gap(16),
                                  Text(
                                    "Classes details",
                                    style: title3TextSemiBold,
                                  ),
                                  const Spacer(),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12.0, vertical: 4.0),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      color: adminBusinessController
                                                  .adminClassReportByID
                                                  .value
                                                  .status ==
                                              "inreview"
                                          ? AppColors.kwarning
                                          : adminBusinessController
                                                      .adminClassReportByID
                                                      .value
                                                      .status ==
                                                  "rejected"
                                              ? AppColors.errorRed
                                                  .withOpacity(.2)
                                              : Colors.green.withOpacity(.2),
                                    ),
                                    child: Text(
                                      adminBusinessController
                                          .adminClassReportByID
                                          .value
                                          .status
                                          .capitalizeFirst
                                          .toString(),
                                      style: body2TextBold.copyWith(
                                          color: adminBusinessController
                                                      .adminClassReportByID
                                                      .value
                                                      .status ==
                                                  "inreview"
                                              ? Colors.yellow[800]
                                              : adminBusinessController
                                                          .adminClassReportByID
                                                          .value
                                                          .status ==
                                                      "rejected"
                                                  ? AppColors.errorRed
                                                  : Colors.green),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Divider(
                              thickness: 1.0,
                              color: AppColors.kgrey,
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20.0, vertical: 10),
                              child: Column(
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              adminBusinessController
                                                  .adminClassReportByID
                                                  .value
                                                  .title,
                                              style: heading2TextMedium,
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            kMinHeight,
                                            Row(
                                              children: [
                                                adminBusinessController
                                                            .adminClassReportByID
                                                            .value
                                                            .classType ==
                                                        "Online"
                                                    ? const ClassRowChild(
                                                        iconPath:
                                                            "assets/icons/VideoConference.svg",
                                                        textTitle: "Online")
                                                    : ClassRowChild(
                                                        iconPath:
                                                            "assets/svg/MapPin.svg",
                                                        textTitle:
                                                            adminBusinessController
                                                                .adminClassReportByID
                                                                .value
                                                                .classType,
                                                      ),
                                                const Gap(15),
                                                ClassRowChild(
                                                  iconPath:
                                                      "assets/svg/Baby.svg",
                                                  textTitle:
                                                      "${adminBusinessController.adminClassReportByID.value.minAge} - ${adminBusinessController.adminClassReportByID.value.maxAge} years",
                                                ),
                                                const Gap(15),
                                                ClassRowChild(
                                                    iconPath:
                                                        "assets/svg/group.svg",
                                                    textTitle:
                                                        adminBusinessController
                                                            .adminClassReportByID
                                                            .value
                                                            .classType),
                                              ],
                                            ),
                                            kMinHeight,
                                            Text(
                                              "About Class",
                                              style: title3TextSemiBold,
                                            ),
                                            kSmHeight,
                                            Text(
                                              adminBusinessController
                                                  .adminClassReportByID
                                                  .value
                                                  .description,
                                              maxLines: 6,
                                              style: body2TextMedium.copyWith(
                                                  color: AppColors.secondary),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            const Gap(20),
                                            ListTile(
                                              contentPadding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 0,
                                                      vertical: 0),
                                              title: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "Orgainser",
                                                    style: title3TextSemiBold,
                                                  ),
                                                  const Gap(8),
                                                ],
                                              ),
                                              subtitle: Row(
                                                children: [
                                                  adminBusinessController
                                                          .adminClassReportByID
                                                          .value
                                                          .profilePictureUrl
                                                          .isEmpty
                                                      ? SvgPicture.asset(
                                                          "assets/icons/empty_logo.svg",
                                                          height: 34,
                                                          width: 34,
                                                          fit: BoxFit.cover)
                                                      : CircleAvatar(
                                                          maxRadius: 14,
                                                          backgroundImage: NetworkImage(
                                                              adminBusinessController
                                                                  .adminClassReportByID
                                                                  .value
                                                                  .profilePictureUrl),
                                                        ),
                                                  const Gap(10),
                                                  Text(
                                                    adminBusinessController
                                                        .adminClassReportByID
                                                        .value
                                                        .businessName,
                                                    style: bodyTextRegular,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const Gap(20),
                                      Expanded(
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          child: Image.network(
                                            adminBusinessController
                                                .adminClassReportByID
                                                .value
                                                .bannerUrl,
                                            width: 340,
                                            height: 340,
                                            fit: BoxFit.contain,
                                            loadingBuilder: (context, child,
                                                loadingProgress) {
                                              if (loadingProgress == null) {
                                                return child;
                                              }
                                              return Center(
                                                child:
                                                    CircularProgressIndicator(
                                                  value: loadingProgress
                                                              .expectedTotalBytes !=
                                                          null
                                                      ? loadingProgress
                                                              .cumulativeBytesLoaded /
                                                          loadingProgress
                                                              .expectedTotalBytes!
                                                      : null,
                                                ),
                                              );
                                            },
                                            errorBuilder:
                                                (context, error, stackTrace) {
                                              return Image.asset(
                                                "assets/png/Banner_Placeholder.png",
                                                width: 340,
                                                height: 340,
                                                fit: BoxFit.contain,
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            const Divider(
                              thickness: 1.0,
                              color: AppColors.kgrey,
                            ),
                            const ClassCategoryRow(
                              title: 'Category',
                              content: "-",
                              //  adminBusinessController
                              //     .adminClassReportByID.value.category
                            ),
                            const Divider(
                              thickness: 1.0,
                              color: AppColors.kgrey,
                            ),
                            const ClassCategoryRow(
                              title: 'Sub-Category',
                              content: "-",
                              // adminBusinessController
                              //     .adminClassReportByID.value.subCategory,
                            ),
                            const Divider(
                              thickness: 1.0,
                              color: AppColors.kgrey,
                            ),
                            ClassCategoryRow(
                                title: 'Class fee',
                                content: adminBusinessController
                                            .adminClassReportByID.value.price ==
                                        0
                                    ? "Free"
                                    : "₹${adminBusinessController.adminClassReportByID.value.price} ${adminBusinessController.adminClassReportByID.value.price == 1 ? '' : 'onwards'}"),
                            classVM.classDetailsModel.value.ctamobile.isEmpty
                                ? const SizedBox.shrink()
                                : Column(
                                    children: [
                                      const Divider(
                                        thickness: 1.0,
                                        color: AppColors.kgrey,
                                      ),
                                      ClassCategoryRow(
                                        title: 'Phone',
                                        content: adminBusinessController
                                            .adminClassReportByID
                                            .value
                                            .ctaMobile,
                                      ),
                                    ],
                                  ),
                            const Divider(
                              thickness: 1.0,
                              color: AppColors.kgrey,
                            ),
                            ClassCategoryRow(
                              title: 'City',
                              content: adminBusinessController
                                  .adminClassReportByID.value.city,
                            ),
                            const Divider(
                              thickness: 1.0,
                              color: AppColors.kgrey,
                            ),
                            ClassCategoryRow(
                              title: 'Address',
                              content: adminBusinessController
                                  .adminClassReportByID.value.address,
                            ),
                            Divider(
                              thickness: 1.0,
                              color: AppColors.ktertiary.withOpacity(.3),
                            ),
                            ActivityLog(
                              entries: adminBusinessController
                                  .adminClassReportByID.value.activityLogs
                                  .map((log) {
                                return ActivityEntry(
                                  title: log.message,
                                  dateTime: DateTime.parse(log.time),
                                );
                              }).toList(),
                            ),
                            const Gap(10),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              child: Text("Report summary",
                                  style: title3TextSemiBold),
                            ),
                            const Gap(10),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(
                                      minWidth:
                                          MediaQuery.of(context).size.width),
                                  child: DataTable(
                                    headingTextStyle: body2TextRegular.copyWith(
                                        color: AppColors.txtsecondary),
                                    dataTextStyle: bodyTextRegular,
                                    sortColumnIndex: sortColumnIndex,
                                    sortAscending: sortAscending,
                                    border: TableBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      left: const BorderSide(
                                          color: AppColors.bordergrey),
                                      right: const BorderSide(
                                          color: AppColors.bordergrey),
                                      top: const BorderSide(
                                          color: AppColors.bordergrey),
                                      bottom: const BorderSide(
                                          color: AppColors.bordergrey),
                                      verticalInside: BorderSide.none,
                                    ),
                                    headingRowColor:
                                        MaterialStateColor.resolveWith(
                                            (states) => AppColors.klightwhite),
                                    columnSpacing: 20,
                                    dataRowMaxHeight: 70,
                                    columns: [
                                      DataColumn(
                                          label: Text('Sr No',
                                              style: body2TextRegular.copyWith(
                                                  color:
                                                      AppColors.txtsecondary))),
                                      DataColumn(
                                          label: Text('Reported by',
                                              style: body2TextRegular.copyWith(
                                                  color:
                                                      AppColors.txtsecondary))),
                                      DataColumn(
                                          label: Text('Reported on',
                                              style: body2TextRegular.copyWith(
                                                  color:
                                                      AppColors.txtsecondary))),
                                      DataColumn(
                                          label: Text('Reason',
                                              style: body2TextRegular.copyWith(
                                                  color:
                                                      AppColors.txtsecondary))),
                                      DataColumn(
                                          label: Text('Class name',
                                              style: body2TextRegular.copyWith(
                                                  color:
                                                      AppColors.txtsecondary))),
                                      DataColumn(
                                          label: Text('Attachments',
                                              style: body2TextRegular.copyWith(
                                                  color:
                                                      AppColors.txtsecondary))),
                                    ],
                                    rows: List<DataRow>.generate(
                                      adminBusinessController
                                          .adminClassReportByID
                                          .value
                                          .reportSummary
                                          .length,
                                      (index) {
                                        final report = adminBusinessController
                                            .adminClassReportByID
                                            .value
                                            .reportSummary[index];
                                        return DataRow(
                                          cells: [
                                            DataCell(Text("${index + 1}",
                                                style: bodyTextRegular)),
                                            DataCell(Text(report.reportedBy,
                                                style: bodyTextRegular)),
                                            DataCell(Text(
                                                _formatDateTime(DateTime.parse(
                                                    report.reportedOn)),
                                                style: bodyTextRegular)),
                                            DataCell(Text(report.reason,
                                                style: bodyTextRegular)),
                                            DataCell(
                                              SizedBox(
                                                width: 240,
                                                child: Text(
                                                  report.eventName,
                                                  maxLines: 4,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: bodyTextRegular,
                                                ),
                                              ),
                                            ),
                                            DataCell(Center(
                                              child: IconButton(
                                                  onPressed: () {
                                                    log("Attachments");
                                                    _showImageDialog(context,
                                                        report.attachment);
                                                  },
                                                  icon: const Icon(
                                                      Icons
                                                          .remove_red_eye_outlined,
                                                      color: AppColors
                                                          .kprimarycolor)),
                                            )),
                                          ],
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Obx(() => adminBusinessController
                                        .adminClassReportByID.value.status ==
                                    "inreview"
                                ? Padding(
                                    padding: const EdgeInsets.all(20),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Divider(),
                                        const Gap(30),
                                        Text("Take Action",
                                            style: title3TextSemiBold),
                                        const Gap(20),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text("Choose your response",
                                                      style: body2TextRegular),
                                                  const Gap(10),
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                        child: InkWell(
                                                          onTap: () {
                                                            setState(() {
                                                              isApproveReject
                                                                  .value = true;
                                                              isRejectReport
                                                                      .value =
                                                                  false;
                                                            });
                                                          },
                                                          child: Container(
                                                            height: 42,
                                                            decoration:
                                                                BoxDecoration(
                                                              borderRadius:
                                                                  const BorderRadius
                                                                      .horizontal(
                                                                left: Radius
                                                                    .circular(
                                                                        12),
                                                              ),
                                                              color: isApproveReject
                                                                      .value
                                                                  ? AppColors
                                                                      .kwhite
                                                                  : AppColors
                                                                      .scaffoldColor,
                                                              border:
                                                                  Border.all(
                                                                color: isApproveReject
                                                                        .value
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .bordergrey,
                                                              ),
                                                            ),
                                                            child: Center(
                                                              child: Text(
                                                                "Approve report",
                                                                style: body2TextSemiBold.copyWith(
                                                                    color: isApproveReject.value
                                                                        ? AppColors
                                                                            .txtprimary
                                                                        : AppColors
                                                                            .txtsecondary),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: InkWell(
                                                          onTap: () {
                                                            setState(() {
                                                              isApproveReject
                                                                      .value =
                                                                  false;
                                                              isRejectReport
                                                                  .value = true;
                                                            });
                                                          },
                                                          child: Container(
                                                            height: 42,
                                                            decoration:
                                                                BoxDecoration(
                                                              borderRadius:
                                                                  const BorderRadius
                                                                      .horizontal(
                                                                right: Radius
                                                                    .circular(
                                                                        12),
                                                              ),
                                                              color: isRejectReport
                                                                      .value
                                                                  ? AppColors
                                                                      .kwhite
                                                                  : AppColors
                                                                      .scaffoldColor,
                                                              border:
                                                                  Border.all(
                                                                color: isRejectReport
                                                                        .value
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .bordergrey,
                                                              ),
                                                            ),
                                                            child: Center(
                                                              child: Text(
                                                                "Reject report",
                                                                style: body2TextSemiBold.copyWith(
                                                                    color: isRejectReport.value
                                                                        ? AppColors
                                                                            .txtprimary
                                                                        : AppColors
                                                                            .txtsecondary),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  isRejectReport.value
                                                      ? Form(
                                                          key: _formKey,
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Text(
                                                                  "Describe the reason* (This will be conveyed to the organiser)",
                                                                  style:
                                                                      body2TextRegular),
                                                              const Gap(10),
                                                              CustomTextFormField(
                                                                controller:
                                                                    _reasonController
                                                                        .value,
                                                                onChanged:
                                                                    (val) {},
                                                                hintText:
                                                                    "Write here",
                                                                maxLines: 4,
                                                                maxLength: 150,
                                                                validator:
                                                                    (value) {
                                                                  if (value ==
                                                                          null ||
                                                                      value
                                                                          .isEmpty) {
                                                                    return 'Please enter the reason.';
                                                                  }
                                                                  return null;
                                                                },
                                                              ),
                                                            ],
                                                          ),
                                                        )
                                                      : const SizedBox.shrink(),
                                                  // Column(
                                                  //         crossAxisAlignment:
                                                  //             CrossAxisAlignment
                                                  //                 .start,
                                                  //         children: [
                                                  //           Text(
                                                  //               "Reason for rejection*",
                                                  //               style:
                                                  //                   body2TextRegular),
                                                  //           const Gap(10),
                                                  //           CustomDropdownFormField(
                                                  //             items: rejectReaons
                                                  //                 .map(
                                                  //                   (item) =>
                                                  //                       DropdownMenuItem(
                                                  //                     value: item,
                                                  //                     child:
                                                  //                         Text(item),
                                                  //                   ),
                                                  //                 )
                                                  //                 .toList(),
                                                  //             hintText: "Select",
                                                  //             onChanged: (value) {
                                                  //               selectedRejectReason =
                                                  //                   value!;
                                                  //               log("Selected Rejection Reason: $selectedRejectReason");
                                                  //             },
                                                  //             validator: (value) {
                                                  //               if (value == null ||
                                                  //                   value.isEmpty) {
                                                  //                 return 'Please select a reason';
                                                  //               }
                                                  //               return null;
                                                  //             },
                                                  //           ),
                                                  //         ],
                                                  //       )
                                                  //     : Column(
                                                  //         crossAxisAlignment:
                                                  //             CrossAxisAlignment
                                                  //                 .start,
                                                  //         children: [
                                                  //           Text(
                                                  //               "Describe the reason* (This will be conveyed to the organiser)",
                                                  //               style:
                                                  //                   body2TextRegular),
                                                  //           const Gap(10),
                                                  //           CustomTextFormField(
                                                  //             controller:
                                                  //                 _reasonController
                                                  //                     .value,
                                                  //             onChanged: (val) {},
                                                  //             hintText: "Write here",
                                                  //             maxLines: 4,
                                                  //             maxLength: 150,
                                                  //             validator: (value) {
                                                  //               if (value == null ||
                                                  //                   value.isEmpty) {
                                                  //                 return 'Please enter the reason.';
                                                  //               }
                                                  //               return null;
                                                  //             },
                                                  //           ),
                                                  //         ],
                                                  //       ),
                                                  const Gap(30),
                                                  PrimaryButton(
                                                    text: "Confirm Decision",
                                                    onTap: () {
                                                      bool shouldProceed = true;
                                                      if (!isApproveReject
                                                          .value) {
                                                        shouldProceed = _formKey
                                                                .currentState
                                                                ?.validate() ??
                                                            false;
                                                      }

                                                      if (shouldProceed) {
                                                        if (isApproveReject
                                                            .value) {
                                                        } else {
                                                          if (_reasonController
                                                              .value
                                                              .text
                                                              .isEmpty) {
                                                            Get.snackbar(
                                                              'Error',
                                                              'Please describe the reason for rejecting the report.',
                                                              snackPosition:
                                                                  SnackPosition
                                                                      .TOP,
                                                              backgroundColor:
                                                                  AppColors
                                                                      .errorRed,
                                                              maxWidth: 350,
                                                              colorText:
                                                                  AppColors
                                                                      .kwhite,
                                                            );
                                                            return;
                                                          }
                                                        }
                                                        Future<void>
                                                            action() async {
                                                          bool result;
                                                          if (isApproveReject
                                                              .value) {
                                                            result =
                                                                await adminBusinessController
                                                                    .actionAdminReportData(
                                                              id: adminBusinessController
                                                                  .adminClassReportByID
                                                                  .value
                                                                  .classId,
                                                              reason: "",
                                                              type: "class",
                                                              action: "accept",
                                                            );
                                                          } else {
                                                            result =
                                                                await adminBusinessController
                                                                    .actionAdminReportData(
                                                              id: adminBusinessController
                                                                  .adminClassReportByID
                                                                  .value
                                                                  .classId,
                                                              reason:
                                                                  _reasonController
                                                                      .value
                                                                      .text,
                                                              type: "class",
                                                              action: "reject",
                                                            );
                                                          }
                                                          if (result) {
                                                            adminBusinessController
                                                                .getAdminBusinessReportsData(
                                                                    type:
                                                                        "classes");
                                                            locator<NavigationServices>()
                                                                .goBack();
                                                          }
                                                        }

                                                        action();
                                                      }
                                                    },
                                                  ),
                                                  const Gap(30),
                                                ],
                                              ),
                                            ),
                                            const Gap(30),
                                            Expanded(
                                              child: Container(
                                                width: Get.width,
                                                padding:
                                                    const EdgeInsets.all(12),
                                                decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                    border: Border.all(
                                                        color: AppColors
                                                            .bordergrey)),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                        "If you approve this report as valid:",
                                                        style:
                                                            body2TextSemiBold),
                                                    const Gap(5),
                                                    const BulletText(
                                                        text:
                                                            "The event/ class will be unpublished immediately."),
                                                    const Gap(5),
                                                    const BulletText(
                                                        text:
                                                            "The business account will be suspended if reports warnings are ignored"),
                                                    const Gap(15),
                                                    Text(
                                                        "If you approve this report as valid:",
                                                        style:
                                                            body2TextSemiBold),
                                                    const Gap(5),
                                                    const BulletText(
                                                        text:
                                                            "The event/ class will remain live to users"),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  )
                                : const SizedBox.shrink()),
                          ],
                        ),
                ),
              ),
            ),
          ),
        ));
  }

  void _showImageDialog(BuildContext context, String attachment) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          child: Stack(
            children: [
              SizedBox(
                width: Get.width / 2,
                height: 600,
                child: attachment.isNotEmpty
                    ? Image.network(
                        attachment,
                        fit: BoxFit.cover,
                        loadingBuilder: (BuildContext context, Widget child,
                            ImageChunkEvent? loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Center(
                              child: Text('No image available',
                                  style: bodyTextRegular));
                        },
                      )
                    : Center(
                        child:
                            Text('No image available', style: bodyTextRegular)),
              ),
              Positioned(
                top: 6,
                right: 10,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      // shape: BoxShape.circle,
                      color: AppColors.kprimarycolor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 20,
                      color: AppColors.kwhite,
                    ),
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('dd MMM yyyy hh:mma');
    return formatter.format(dateTime);
  }
}

class ActivityLog extends StatelessWidget {
  final List<ActivityEntry> entries;

  const ActivityLog({super.key, required this.entries});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Activity log',
            style: title3TextSemiBold,
          ),
          const Gap(15),
          for (var entry in entries) _buildTimelineEntry(context, entry),
          Divider(
            thickness: 1.0,
            color: AppColors.ktertiary.withOpacity(.3),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineEntry(BuildContext context, ActivityEntry entry) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.kprimarycolor,
              ),
            ),
            if (entry != entries.last)
              Container(
                width: 2,
                height: 40,
                color: AppColors.kprimarycolor,
              ),
          ],
        ),
        const Gap(15),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                entry.title,
                style: body2TextMedium,
              ),
              Text(
                _formatDateTime(entry.dateTime),
                style: body3TextRegular.copyWith(color: AppColors.txtsecondary),
              ),
              if (entry.by != null) Text('By ${entry.by}'),
              if (entry.reason != null)
                Text(
                  'Reason: ${entry.reason}',
                  style:
                      body3TextRegular.copyWith(color: AppColors.txtsecondary),
                ),
              const Gap(15),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('dd MMM yyyy hh:mma');
    return formatter.format(dateTime);
  }
}

class ActivityEntry {
  final String title;
  final DateTime dateTime;
  final String? by;
  final String? reason;

  ActivityEntry({
    required this.title,
    required this.dateTime,
    this.by,
    this.reason,
  });
}

class BulletText extends StatelessWidget {
  final String text;
  const BulletText({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Text("• $text",
        style: body2TextRegular.copyWith(color: AppColors.txtsecondary));
  }
}
