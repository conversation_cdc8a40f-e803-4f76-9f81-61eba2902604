import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/users/admin_user_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/ShimmerWidget.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/kyc_review.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_user/admin_add_new_user.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';

class AdminUserPage extends StatefulWidget {
  const AdminUserPage({super.key});

  @override
  State<AdminUserPage> createState() => _AdminUserPageState();
}

class _AdminUserPageState extends State<AdminUserPage> {
  final AdminUserController adminUserController = Get.find<AdminUserController>();
  int sortColumnIndex = 0;
  bool sortAscending = false;
  int pageNo = 0;
  String filterBy = "";
  late final int pageCount;
  final int? pages = 1;

  @override
  void initState() {
    adminUserController.getAllAdminUsers();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'User Management',
                    style: heading2TextRegular,
                  ),
                  const Spacer(),
                  PrimaryButton(
                      text: 'Add user',
                      onTap: () {
                        Get.dialog(
                          const KycReviewPanel(
                            content: AdminAddNewUser(),
                          ),
                          barrierDismissible: true,
                        );
                      }),
                ],
              ),
              const Gap(10),
              const Divider(
                color: AppColors.backcolor,
                thickness: 1.0,
              ),
              // const Gap(10),
              kMinHeight,
              buildUsersDataTable(),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildUsersDataTable() {
    return Obx(
      () => adminUserController.isUserLoading.value
          ? const Center(child: CircularProgressIndicator.adaptive())
          : SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: ShimmerWidget(
                isLoading: adminUserController.isUserLoading.value,
                child: DataTable(
                  columnSpacing: 100,
                  headingTextStyle: const TextStyle(
                    color: Colors.black,
                    fontFamily: 'Rubic',
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                  dataTextStyle: const TextStyle(
                    color: Colors.black,
                    fontFamily: 'Rubic',
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  sortColumnIndex: sortColumnIndex,
                  sortAscending: sortAscending,
                  dataRowMaxHeight: 50,
                  border: TableBorder(
                    borderRadius: BorderRadius.circular(12),
                    left: const BorderSide(color: AppColors.bordergrey),
                    right: const BorderSide(color: AppColors.bordergrey),
                    top: const BorderSide(color: AppColors.bordergrey),
                    bottom: const BorderSide(color: AppColors.bordergrey),
                    verticalInside: BorderSide.none,
                  ),
                  headingRowColor: MaterialStateColor.resolveWith(
                      (states) => AppColors.klightwhite),
                  columns: [
                    DataColumn(
                      label: Text('Sr No', style: body2TextBold),
                    ),
                    DataColumn(
                      label: Text('Photo', style: body2TextBold),
                    ),
                    DataColumn(
                      label: Text('Name', style: body2TextBold),
                      tooltip: 'Parent Name',
                    ),
                    DataColumn(
                      label: Text('Mobile', style: body2TextBold),
                      tooltip: 'Mobile',
                    ),
                    DataColumn(
                      label: Text('Role', style: body2TextBold),
                      tooltip:
                          'Super Admin: Full platform access.\nAdmin: Can add, edit, delete, and approve.\nModerator: Can view and approve/reject.',
                    ),
                    DataColumn(
                      label: Text('Action', style: body2TextBold),
                      tooltip: 'Action',
                    ),
                    DataColumn(
                      label: Text('', style: body2TextBold),
                      tooltip: '',
                    ),
                  ],
                  rows: List<DataRow>.generate(
                    adminUserController.adminUserList.length,
                    (index) => DataRow(
                      cells: [
                        DataCell(
                          Text(
                            "${index + 1}",
                          ),
                        ),
                        DataCell(
                          ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: CachedNetworkImage(
                              imageUrl: adminUserController
                                  .adminUserList[index].profilePictureUrl,
                              height: 40.0,
                              width: 40.0,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Image.asset(
                                  "assets/images/class_banner_empty.png",
                                  height: 40.0,
                                  width: 40.0,
                                  fit: BoxFit.cover),
                              errorWidget: (context, url, error) => Image.asset(
                                  "assets/images/class_banner_empty.png",
                                  height: 40.0,
                                  width: 40.0,
                                  fit: BoxFit.cover),
                            ),
                          ),
                        ),
                        DataCell(
                          Text(
                            adminUserController
                                .adminUserList[index].name.capitalizeFirst
                                .toString(),
                            style: body2TextRegular.copyWith(
                                fontWeight: FontWeight.w400),
                          ),
                        ),
                        DataCell(Text(
                          adminUserController
                                  .adminUserList[index].mobile.isEmpty
                              ? "-"
                              : adminUserController.adminUserList[index].mobile,
                          style: body2TextRegular.copyWith(
                              fontWeight: FontWeight.w400),
                        )),
                        DataCell(Text(
                          adminUserController.adminUserList[index].position,
                          style: body2TextRegular.copyWith(
                              fontWeight: FontWeight.w400),
                        )),
                        DataCell(
                          adminUserController.adminUserList[index].name ==
                                  "system"
                              ? const Text("")
                              : Bounceable(
                                  onTap: () {
                                    showDialog(
                                      context: context,
                                      barrierDismissible: false,
                                      builder: (BuildContext context) {
                                        return CustomDialog(
                                          onConfirmTxt: "Delete",
                                          onCancelText: "Cancel",
                                          title: 'Remove user',
                                          content:
                                              'Are you sure you want to delete remove this user.',
                                          image: "assets/icons/Trash_red.svg",
                                          onConfirm: () {
                                            Navigator.pop(context);
                                            adminUserController
                                                .deleteAdminUser(
                                                    adminUserController
                                                        .adminUserList[index]
                                                        .adminId)
                                                .then((value) {
                                              if (value) {
                                                adminUserController
                                                    .getAllAdminUsers();
                                              } else {
                                                Get.snackbar("Error",
                                                    "Can not able to delete user, try again after sometime.");
                                              }
                                            });
                                          },
                                          onCancel: () {
                                            Navigator.of(context).pop();
                                          },
                                        );
                                      },
                                    );
                                  },
                                  child: const Icon(
                                    Icons.delete,
                                    color: AppColors.kprimarycolor,
                                  ),
                                ),
                        ),
                        DataCell(Text(
                          "",
                          style: body2TextRegular.copyWith(
                              fontWeight: FontWeight.w400),
                        )),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }
}

class UserCell extends StatelessWidget {
  const UserCell({super.key, required this.adminUsersModel});
  final AdminUsersModel adminUsersModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.backcolor, width: 1.0),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(
            'assets/svg/User.svg',
            height: 80,
          ),
          const Gap(10),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                adminUsersModel.name,
                style: body2TextBold,
              ),
              const Gap(10),
              Text(
                "+91-${adminUsersModel.mobile}",
                style: body2TextBold,
              ),
              const Gap(10),
              Text(
                adminUsersModel.position,
                style: body2TextBold,
              ),
            ],
          )
        ],
      ),
    );
  }
}
