name: On Deploy

on:
  push:
    branches:
      - stag_deployment

jobs:
  build_and_deploy_web:
    name: Build & Deploy Flutter to Web
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.19.5"
          channel: "stable"

      - name: Enable Web
        run: flutter config --enable-web

      - name: Get Packages
        run: |

          flutter pub get

      - name: Build Web Application
        run: |

          flutter build web

      - name: Make Copy of Artifacts
        run: |

          chmod u+x "${GITHUB_WORKSPACE}/createandcopytofolder.sh"
          bash "${GITHUB_WORKSPACE}/createandcopytofolder.sh"

      - name: Commit Artifacts
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: Commit the artifacts.
